#!/usr/bin/env python
# coding: utf-8

# # DRAFT under construction 
# 
# 
# 
# # What is about ?
# 
# Some first look Ribonanza Kaggle challenge. 
# 
# Notebook partly forked from IAFOSS https://www.kaggle.com/code/iafoss/rna-starter-0-186-lb - please upvote !
# 
# 
# ## Some EDA notes:
# 
# - Train: 821840 samples, Test: 1343823 samples. 
# 
# - Lengths of train rna seq are mostly equal to 177 (95%) with small number of 170 (30000/2?), 115 (27290/2?), 155 (13038/2?) 206 (4998/2?), in test we have 1 million of sequences of length 207 (probably private), 330+K - 177, and few thousands 457, 307 
# 
# - begining 26 symbols of ALL train sequnces are the same: "GGGAACGACUCGAGUAGAGUCGAAAA", up to position 34 there is dominance of the sequence "GGGAACGACUCGAGUAGAGUCGAAAAGAUAUGGA", from the position 35 it becomes more random - see below and discussion:   https://www.kaggle.com/competitions/stanford-ribonanza-rna-folding/discussion/442561 . However for the train sequences of length 206 - they is domination by 3 sequences till 26+90 position. 
# 
# - Train labels are mostly placed at positions 26-125, other positions are mostly NANs. While we need to predict full length lables in test ! 
# 
# - tail sequence mostly "AAAAGAAACAACAACAACAAC" -  21 symbols 
# 
# - First 26 target lablels are NAN (it correposnd to exactly the same 26 symbols at start for all sequences). We mostly have labels for the next  100 positions, and small amount of labels later positions. That does NOT correspond to the fact that only last 21 symbols are exactly the same. Also always NAN in train targets for reactivity_IX ,  IX = 166..206 and the  same for reactivity_error
# 
# 
#     There are some non-nans at exceptional positions: 
#     3 positions: reactivity_0127,reactivity_0128,reactivity_0129 -  47791 - exceptional lengths 170,155,206
#     5 positions: reactivity_0131 ...reactivity_0135, count:   17813 - exceptional lengths 155,206
#     30 positions at reactivity_0136 ... reactivity_0165 count: 4998 - exactly those of length 206
# 
# 
# - For 2A3 median value seems to decrease with position (SN_filter = 1)
# 
# - There are 2150418 files in the competition folder see https://www.kaggle.com/code/alexandervc/ribonanza-show-2-millions-files
# 
# - Only 29 (2A3), 26 (DMS) percents of targets are within "good" range 0,1 
# 
# - 45 (2A3), 33 (DMS) percents of targets are EXACTLY zero
# 
# - there are some duplicated sequences/sequence_ids - 806573 - unique out of 821840 -  probably experiments were made with different conditions or just source databases are different
# 
# 
# - Each sequence comes with "family" of similar sequences which size varies from 3 thousands to about just 50 - seen by Levenshtein distances - these similar rna should probably grouped togather for groupwise CV - similar to: https://www.kaggle.com/code/alexandervc/cafa5-23-groups-and-folds-diamond-igraph 
# 
# 
# 
# - IAFOSS's transformer model take about 3h for one fold on Kaggle GPU P100 - 30 epochs - see below logs on error decrease . 
# 

# In[1]:


# This Python 3 environment comes with many helpful analytics libraries installed
# It is defined by the kaggle/python Docker image: https://github.com/kaggle/docker-python
# For example, here's several helpful packages to load

import time
t0start = time.time() 

import numpy as np # linear algebra
import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)

import matplotlib.pyplot as plt
import seaborn as sns


# Input data files are available in the read-only "../input/" directory
# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory

import os
cc = 0
for dirname, _, filenames in os.walk('/kaggle/input'):
    for filename in filenames:
        cc += 1
        if cc < 20:
            print(os.path.join(dirname, filename))
        else:
            break
    if cc < 20:
        pass
    else:
        break
            
# You can write up to 20GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using "Save & Run All" 
# You can also write temporary files to /kaggle/temp/, but they won't be saved outside of the current session


# # Train Data Load
# 
# 
# From IAFOSS: 
# 
# The primary training data is provided in train_data.csv, which contains 821840 RNA sequences and the corresponding reactivity measurements with 2A3_MaP and DMS_MaP methods. The reactivity is reported in columns reactivity_0001 - reactivity_0206 and is set to NaN for the first 26 and the last 21 nucleotides as well as padding for sequences shorter than 206. For faster loading and effective RAM use, I converted the data into a float32 parquet file. 
# 
# Evaluation in this competition is performed only on samples with SN_filter = 1 for both measurement methods. In this example, I perform training only on samples wiht SN_filter = 1, which gives a noticeable CV boost but uses only 1/4 of the data (i.e. training on noisy SN_filter = 0 data degrades the performance). A proper consideration of all data as well as reactivity errors may boost the performance.
# 
# In this example, I use a simple CV Kfold split. However, given a mismatch in the RNA length between train/public LB vs. private LB data, **it may be important to verify the effect of the sequence length** to avoid a significant shakeup at the private LB.
# 
# One of the tricks, well known in NLP community, which I use here, is length matching batch sampling: composing batches of samples of approximately the same length to minimize the overhead caused by padding tokens.

# In[2]:


get_ipython().run_cell_magic('time', '', "PATH = '/kaggle/input/stanford-ribonanza-rna-folding-converted/'\ndf = pd.read_parquet(os.path.join(PATH,'train_data.parquet'))\nprint( df.shape )\ndf\n")


# In[3]:


df['len'] = [len(t) for t in df['sequence']]
print( df['len'].value_counts() )
print()
m = df['SN_filter'] == 1
print('SN_filter == 1:', m.sum())
print( df['len'][m].value_counts() )


# In[4]:


print( df['experiment_type'].value_counts() )
print()
print("len(set(df['sequence'])):", len(set(df['sequence'])))
print();

m = df['SN_filter'] == 1
print('SN_filter == 1:', m.sum())
print( df['experiment_type'][m].value_counts() )


# In[5]:


df.columns[6], df.columns[7]


# In[6]:


df.head(1)


# # PCA/UMAP Onehot encoded sequences

# In[7]:


get_ipython().run_cell_magic('time', '', "m1 = df['experiment_type'] == 'DMS_MaP'\nprint(df[m1]['len'].value_counts() )\nprint()\n\ndict_len_to_X = {}\nfor L in [155 ,170,177,206]:# 170\n\n    m1 = df['experiment_type'] == 'DMS_MaP'\n    m = m1 & ( df['len'] == L)\n    print(L, m1.sum(), m.sum() )\n#     print(m.sum() )\n\n    import time\n    t0 = time.time()\n    rna_onehot_dict = {'A': [1,0,0,0], 'U': [0,1,0,0], 'G':[0,0,1,0], 'C': [0,0,0,1],}\n\n    v = df['sequence'][m].values\n\n    N = m.sum()\n    X = np.zeros( (N, L*4) , np.int8)\n    for k in range(N):\n        seq = v[k]\n#         if k<=1: print( k, 'len(seq):', len(seq) )\n#         if (k % 400_000 == 0): print(k, '%.1f seconds passed'%(time.time()-t0))\n        l = [rna_onehot_dict[seq[II]] for II in range(L) ]\n        X[k,:] = np.array(l).ravel()\n        \n#     dict_len_to_X[L] = X\n    \n    \n    from sklearn.decomposition import PCA\n    str_inf = 'PCA'\n    reducer = PCA(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Onehot encoded seqences reduced. Train. Len ' + str(L) + ' Count ' +str(Xr.shape[0] ) , fontsize = 20)\n\n        plt.show()\n\n    import umap\n    str_inf = 'umap' \n    reducer = umap.UMAP(n_components=2)\n    Xr = reducer.fit_transform(X[:150_000,:])\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Onehot encoded seqences reduced. Train. Len ' + str(L) + ' Count ' +str(Xr.shape[0]) , fontsize = 20)\n        plt.show()\n    \n    \n")


# # Statistics for each nucleotide depending on position

# In[8]:


m1 = df['experiment_type'] == 'DMS_MaP'
df[m1]['len'].value_counts()


# In[9]:


get_ipython().run_cell_magic('time', '', "dict_len_to_dfstat = {}\nfor L in [155,170,177,206]:# 170\n\n    m1 = df['experiment_type'] == 'DMS_MaP'\n    m = m1 & ( df['len'] == L)\n    print(L, m1.sum(), m.sum() )\n#     print(m.sum() )\n\n\n    import time\n    t0 = time.time()\n    rsna_dict = {'A': 0, 'U': 1, 'G':2, 'C': 3}\n\n    v = df['sequence'][m].values\n\n    N = m.sum()\n    res = np.zeros( (N, L) , np.int8)\n    for k in range(N):\n        seq = v[k]\n#         if k<=1: print( k, 'len(seq):', len(seq) )\n#         if (k % 400_000 == 0): print(k, '%.1f seconds passed'%(time.time()-t0))\n        l = [rsna_dict[seq[II]] for II in range(L) ]\n        res[k,:] = l\n    res.shape  \n\n    d = pd.DataFrame(index = [0,1,2,3])\n    for k in range(0,res.shape[1]):\n        v = pd.Series(res[:,k]).value_counts()\n        v.name = str(k)\n        d =d.join(v)\n#     print( )\n\n    dict_num2symbol = { rsna_dict[k]:k for k in rsna_dict}\n    d.index = [dict_num2symbol[k] for k in d.index]\n    dict_len_to_dfstat[L] = d\n\n    plt.figure(figsize = (20,6) )\n    for i in range(4):\n        pass\n        v = d.iloc[i,:].values\n        lb =  dict_num2symbol[i]\n        plt.plot(v,'.', label = lb )\n    #     plt.plot(d.iloc[i,:-21].values , label = rsna_dict[i]  )\n    plt.legend()\n    plt.xlabel('position on RNA',fontsize = 20)\n    plt.title('Numbers of nucleotides for different positions. Train Len ' + str(L) + ' Count ' +str(m.sum() ) , fontsize = 20)\n    plt.show()\n")


# In[10]:


dict_len_to_dfstat[206].iloc[:,26:46]


# # Sequences on length 206 in train are dominated by 3 sequences at begining till 26+90 positions

# In[11]:


m = df['len'] == 206
print(m.sum())
for i in range(10):
    seq = df[m]['sequence'].iat[i]
    print(seq[26:56],i)


# In[12]:


m = df['len'] == 206
print(m.sum())
for N in [80,90,100,150]:
    l = [t[26: N] for t in df['sequence'][m] ]
    print('First 26+N ', N)
    print( pd.Series(l).value_counts().head(5) )


# In[13]:


dict_len_to_dfstat[170].iloc[:,126:136]


# In[14]:


dict_len_to_dfstat[177].iloc[:,128:136]


# In[15]:


# sanity check:
dict_len_to_dfstat[177].iloc[:,126:136].sum()


# # PCA/UMAP for sequences ordinally encoded

# In[16]:


get_ipython().run_cell_magic('time', '', "dict_len_to_dfstat = {}\nfor L in [155,170,177,206]:# 170\n\n    m1 = df['experiment_type'] == 'DMS_MaP'\n    m = m1 & ( df['len'] == L)\n    print(L, m1.sum(), m.sum() )\n#     print(m.sum() )\n\n\n    import time\n    t0 = time.time()\n    rsna_dict = {'A': 0, 'U': 1, 'G':2, 'C': 3}\n\n    v = df['sequence'][m].values\n\n    N = m.sum()\n    res = np.zeros( (N, L) , np.int8)\n    for k in range(N):\n        seq = v[k]\n#         if k<=1: print( k, 'len(seq):', len(seq) )\n#         if (k % 400_000 == 0): print(k, '%.1f seconds passed'%(time.time()-t0))\n        l = [rsna_dict[seq[II]] for II in range(L) ]\n        res[k,:] = l\n    res.shape  \n\n    from sklearn.decomposition import PCA\n    X = res\n    str_inf = 'PCA'\n    reducer = PCA(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Ordinal encoded seqences reduced. Train. Len ' + str(L) + ' Count ' +str(m.sum() ) , fontsize = 20)\n\n        plt.show()\n\n    import umap\n    str_inf = 'umap' \n    reducer = umap.UMAP(n_components=2)\n    X = res\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Ordinal encoded seqences reduced. Train. Len ' + str(L) + ' Count ' +str(m.sum() ) , fontsize = 20)\n        plt.show()\n    \n")


# # Dependence of medians on position 
# 
# For 2A3 median seems to decrease with position 

# In[17]:


m = df['SN_filter'] == 1
m1 = df['experiment_type'] == '2A3_MaP'
print( m.sum(), (m&m1).sum() )
m1 = df['experiment_type'] == 'DMS_MaP'
print( m.sum(), (m&m1).sum() )


# In[18]:


get_ipython().run_cell_magic('time', '', "\nm = df['SN_filter'] == 1\nm1 = df['experiment_type'] == '2A3_MaP'\nv  = df[m&m1].iloc[:,33:137].median(axis = 0)\nv = v.round(3)\nprint(list(np.round(v.values,3)))\nplt.figure(figsize = (20,4))\nplt.plot(v.values[:90],'*-')\nplt.title('2A3',fontsize = 20)\nplt.show()\n\nm1 = df['experiment_type'] == 'DMS_MaP'\nv  = df[m&m1].iloc[:,33:137].median(axis = 0)\nv = v.round(3)\nprint(list(np.round(v.values,3)))\nplt.figure(figsize = (20,4))\nplt.plot(v.values[:90],'*-')\nplt.title('DMS',fontsize = 20)\nplt.show()\n")


# # Reactivity_IX always NAN for IX = 1..26, IX = 166..206, same for error

# In[19]:


get_ipython().run_cell_magic('time', '', "v = (~np.isnan(df.iloc[:,7:].values)).sum(axis = 0 )\ns = pd.Series(index = df.columns[7:], data = v)\nl = [t for t in list(s[s==0].index ) if 'error' not in t ]\nprint( l)\n")


# In[20]:


l = [t for t in list(s[s==0].index ) if 'error' in t ]
print( l)


# ## Look on each position interval more closely 

# In[21]:


# 1643680 
print(list(v[26:126])) # 


# In[22]:


print(list(v[126:166]))


# In[23]:


s.iloc[126:130] # 3 positions: reactivity_0127,reactivity_0128,reactivity_0129 -  47791


# In[24]:


get_ipython().run_cell_magic('time', '', "m = df['reactivity_0127'].notnull()\nl = [len(t) for t in df[m]['sequence'] ]\npd.Series(l).value_counts()\n")


# In[25]:


# print( s.iloc[130:132] )
print( s.iloc[130:136] ) # 5 positions: reactivity_0131 ...reactivity_0135, count:   17813


# In[26]:


get_ipython().run_cell_magic('time', '', "m = df['reactivity_0131'].notnull()\nl = [len(t) for t in df[m]['sequence'] ]\npd.Series(l).value_counts()\n")


# In[27]:


print( (s.iloc[130:170] == 4998 ).sum() )# 30 positions at reactivity_0136 ... reactivity_0165 count: 4998
s.iloc[164:167]


# In[28]:


get_ipython().run_cell_magic('time', '', "l = [len(t)==206 for t in df['sequence']]\nnp.sum(l)\n")


# In[29]:


get_ipython().run_cell_magic('time', '', "l2 = [ ~np.isnan(t) for t in df['reactivity_0136'] ]\nnp.sum(l2)\n")


# In[30]:


l2 == l


# # First 26 symbols are the same, disbalance in 27-34 , GAUAUGGA
# 
# 
#     Same 26 start for all 
#     GGGAACGACUCGAGUAGAGUCGAAAA
#     Top represented 34 with frequency 232092 (next is 31334)
#     GGGAACGACUCGAGUAGAGUCGAAAAGAUAUGGA    
#     
#     From length 35 disbalance become to disappear
# 

# In[31]:


get_ipython().run_cell_magic('time', '', "# len('GGGAACGACUCGAGUAGAGUCGAAAA') = 26\n\n\nfor N in [26,27, 28,33,34,35,36]:\n    print( N)\n    l = [s[:N] for s in df['sequence'] ]\n    print( pd.Series(l).value_counts().head(10) )\n    print()\n")


# # Tail AAAAGAAACAACAACAACAAC  - mostly these 21 symbol 

# In[32]:


l = [len(s) for s in df['sequence'] ]
pd.Series(l).value_counts()


# In[33]:


get_ipython().run_cell_magic('time', '', "print(len('AAAAGAAACAACAACAACAAC'))\nprint(df.shape)\nfor N in [155,156,160]:\n    print( N)\n    l = [s[N:] for s in df['sequence'] ]\n    print( pd.Series(l).value_counts().head(10) )\n    print()\n")


# In[34]:


df.columns[7]


# # Positions where number of NANs changes 
# 
# The main change is of couse in position at position i = 25,26 - first 26 are totally NAN, from 27 - mostly not NAN 

# In[35]:


get_ipython().run_cell_magic('time', '', 'v = df.iloc[:,7:].isnull().sum(axis = 0 )\nfor k in range(len(v)-1):\n    if v[k]!=v[k+1]:print(k,v[k],v[k+1], v.index[k], v.index[k+1])\n')


# # Load test data

# In[36]:


get_ipython().run_cell_magic('time', '', "fn = '/kaggle/input/stanford-ribonanza-rna-folding-converted/test_sequences.parquet'\ndft = pd.read_parquet(fn)\nprint(dft.shape)\ndft\n")


# In[37]:


get_ipython().run_cell_magic('time', '', "dft['len'] = [len(t) for t in dft['sequence']]\ndft['len'].value_counts()\n")


# # Test. PCA/UMAP onehot encoded sequences 

# In[38]:


get_ipython().run_cell_magic('time', '', "\ndict_len_to_X = {}\nfor L in [177,207,307,457]:# 170\n\n#     m1 = dft['experiment_type'] == 'DMS_MaP'\n    m =  ( dft['len'] == L)\n    print(L, m1.sum(), m.sum() )\n#     print(m.sum() )\n\n    import time\n    t0 = time.time()\n    rna_onehot_dict = {'A': [1,0,0,0], 'U': [0,1,0,0], 'G':[0,0,1,0], 'C': [0,0,0,1],}\n\n    v = dft['sequence'][m].values\n\n    N = m.sum()\n    X = np.zeros( (N, L*4) , np.int8)\n    for k in range(N):\n        seq = v[k]\n#         if k<=1: print( k, 'len(seq):', len(seq) )\n#         if (k % 400_000 == 0): print(k, '%.1f seconds passed'%(time.time()-t0))\n        l = [rna_onehot_dict[seq[II]] for II in range(L) ]\n        X[k,:] = np.array(l).ravel()\n        \n#     dict_len_to_X[L] = X\n    \n    \n    from sklearn.decomposition import PCA\n    str_inf = 'PCA'\n    reducer = PCA(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Onehot encoded seqences reduced. Test. Len ' + str(L) + ' Count ' +str(Xr.shape[0] ) , fontsize = 20)\n\n        plt.show()\n\n    import umap\n    str_inf = 'umap' \n    reducer = umap.UMAP(n_components=2)\n    Xr = reducer.fit_transform(X[:50_000,:])\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Onehot encoded seqences reduced. Test. Len ' + str(L) + ' Count ' +str(Xr.shape[0]) , fontsize = 20)\n        plt.show()\n    \n    import gc\n    gc.collect()\n")


# # Nucleotide count statistics for each position. Test

# In[39]:


get_ipython().run_cell_magic('time', '', "dict_len_to_dfstat = {}\nfor L in [177,207,307,457]:# 170\n\n#     m1 = df['experiment_type'] == 'DMS_MaP'\n    m =( dft['len'] == L)\n    print(L,  m.sum() )\n#     print(m.sum() )\n\n\n    import time\n    t0 = time.time()\n    rsna_dict = {'A': 0, 'U': 1, 'G':2, 'C': 3}\n\n    v = dft['sequence'][m].values\n\n    N = m.sum()\n    res = np.zeros( (N, L) , np.int8)\n    for k in range(N):\n        seq = v[k]\n#         if k<=1: print( k, 'len(seq):', len(seq) )\n#         if (k % 400_000 == 0): print(k, '%.1f seconds passed'%(time.time()-t0))\n        l = [rsna_dict[seq[II]] for II in range(L) ]\n        res[k,:] = l\n    res.shape  \n\n    d = pd.DataFrame(index = [0,1,2,3])\n    for k in range(0,res.shape[1]):\n        v = pd.Series(res[:,k]).value_counts()\n        v.name = str(k)\n        d =d.join(v)\n#     print( )\n\n    dict_num2symbol = { rsna_dict[k]:k for k in rsna_dict}\n    d.index = [dict_num2symbol[k] for k in d.index]\n    dict_len_to_dfstat[L] = d\n\n    plt.figure(figsize = (20,6) )\n    for i in range(4):\n        pass\n        v = d.iloc[i,:].values\n        lb =  dict_num2symbol[i]\n        plt.plot(v,'.', label = lb )\n    #     plt.plot(d.iloc[i,:-21].values , label = rsna_dict[i]  )\n    plt.legend()\n    plt.xlabel('position on RNA',fontsize = 20)\n    plt.title('Numbers of nucleotides for different positions. Test. Len ' + str(L) + ' Count ' +str(m.sum() ) , fontsize = 20)\n    plt.show()\n")


# # Test. Dim-reductions sequences ordinal encoded

# In[40]:


get_ipython().run_cell_magic('time', '', "dict_len_to_dfstat = {}\nfor L in [177,207,307,457]:# 170\n\n#     m1 = df['experiment_type'] == 'DMS_MaP'\n    m =( dft['len'] == L)\n    print(L,  m.sum() )\n#     print(m.sum() )\n\n\n    import time\n    t0 = time.time()\n    rsna_dict = {'A': 0, 'U': 1, 'G':2, 'C': 3}\n\n    v = dft['sequence'][m].values\n\n    N = m.sum()\n    res = np.zeros( (N, L) , np.int8)\n    for k in range(N):\n        seq = v[k]\n#         if k<=1: print( k, 'len(seq):', len(seq) )\n#         if (k % 400_000 == 0): print(k, '%.1f seconds passed'%(time.time()-t0))\n        l = [rsna_dict[seq[II]] for II in range(L) ]\n        res[k,:] = l\n    res.shape  \n\n    from sklearn.decomposition import PCA\n    X = res\n    str_inf = 'PCA'\n    reducer = PCA(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Ordinal encoded seqences reduced. Train. Len ' + str(L) + ' Count ' +str(m.sum() ) , fontsize = 20)\n\n        plt.show()\n\n    import umap\n    str_inf = 'umap' \n    reducer = umap.UMAP(n_components=2)\n    X = res[:50_000,:]\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1]]: #  ,[0,2],[1,2]]:\n        plt.figure(figsize = (15,6))\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title('Ordinal encoded seqences reduced. Train. Len ' + str(L) + ' Count ' +str(X.shape[0] ) , fontsize = 20)\n        plt.show()\n    \n")


# ## Check we need to predict all positions , i.e. id_max = id_min + len(sequence)-1

# In[41]:


get_ipython().run_cell_magic('time', '', "l =  [ (dft['id_max'].iat[k] - dft['id_min'].iat[k]+1) == len(dft['sequence'].iat[k]) for k in range(len(dft)) ]\nnp.sum(l)\n")


# In[42]:


len(dft['sequence'].iat[0]), len(dft['sequence'].iat[0])+len(dft['sequence'].iat[1]), len(dft['sequence'].iat[0])+len(dft['sequence'].iat[1]) + len(dft['sequence'].iat[2]) 


# In[43]:


# future - (Boolean) sequences whose data will be collected after the start of the competition (but before final scoring) are labeled as 1.
dft['future'].value_counts()


# # Lengths in test - mostly 207 (1million)  

# In[44]:


get_ipython().run_cell_magic('time', '', "l = [len(t) for t in dft['sequence']] \npd.Series(l).value_counts()\n")


# ## Check how sequences are grouped by length - it will be helpful to prepare submit by X.ravel() - four different subgroups

# In[45]:


get_ipython().run_cell_magic('time', '', "l = [len(t) for t in dft['sequence']] \n# pd.Series(l).value_counts()\n\nfor k in range(len(l)-1):\n    if l[k]!=l[k+1]:\n        print(k,l[k],l[k+1])\n")


# # Test 26 start is the same, but no dominance till 34

# In[46]:


get_ipython().run_cell_magic('time', '', "# len('GGGAACGACUCGAGUAGAGUCGAAAA') = 26\n\nprint( 'GGGAACGACUCGAGUAGAGUCGAAAA' == 'GGGAACGACUCGAGUAGAGUCGAAAA') # First 26 are exactly the same in train and test \nfor N in [26,27, 28,33,34,35,36]:\n    print( N)\n    l = [s[:N] for s in dft['sequence'] ]\n    print( pd.Series(l).value_counts().head(10) )\n    print()\n")


# # Test Tail 21 position is typically same to train 'AAAAGAAACAACAACAACAAC'
# 
# Several repeats of AAC at the end , and before AAAAG 

# In[47]:


get_ipython().run_cell_magic('time', '', "print(len('AAAAGAAACAACAACAACAAC'))\nprint( 'AAAAGAAACAACAACAACAAC' == 'AAAAGAAACAACAACAACAAC', ' test and train top tail are the same  ')\nprint(df.shape)\nfor N in [(207-22), (207-21),(207-20)]:\n    print('N = ', N)\n    l = [s[N:] for s in dft['sequence'] ]\n    print( pd.Series(l).value_counts().head(3) )\n    print()\n")


# # SN_filter seems almost randomly distributed by index, but may be not 100%

# In[48]:


d = df[['SN_filter']].copy()
d['Index'] = range(len(df))
d.corr()


# In[49]:


df[ 'SN_filter'].value_counts()


# In[50]:


get_ipython().run_cell_magic('time', '', "plt.figure(figsize = (20,5))\nplt.plot( df[ 'SN_filter'].values)\nplt.show()\nplt.figure(figsize = (20,5))\nplt.plot( df[ 'SN_filter'].values[:5000])\nplt.show()\n")


# # Look on Targets

# In[51]:


df.iloc[1:10,33:137]


# In[52]:


get_ipython().run_cell_magic('time', '', 'plt.figure(figsize = (20,5))\nfor i in range(33,40):\n    plt.plot(df.iloc[:,i].values)\nplt.show()\n')


# In[53]:


get_ipython().run_cell_magic('time', '', 'plt.figure(figsize = (20,5))\nfor i in range(33,40):\n    plt.plot(np.clip( df.iloc[:,i].values,0,1)  )\nplt.show()\n')


# In[54]:


get_ipython().run_cell_magic('time', '', "for i in range(33,50):\n    plt.figure(figsize = (20,5))\n    plt.plot(df.iloc[:,i].values, label = str(i))\n    plt.grid()\n    plt.title(df.columns[i],fontsize = 20 )\n    plt.xlabel('sample index',fontsize =20 )\n    plt.show()\n    \n")


# In[55]:


get_ipython().run_cell_magic('time', '', 'sns.pairplot(np.clip(df.iloc[:10_000,33:38],0,1)  )\nplt.show()\n')


# # PCA for targets 

# In[56]:


get_ipython().run_cell_magic('time', '', "N0,N1 = 0, 800_000\nfrom sklearn.decomposition import PCA\nX = df.iloc[N0:N1,33:137].fillna(0)\nstr_inf = 'PCA'\nreducer = PCA(n_components=3)\nXr = reducer.fit_transform(X)\nfor i,j in [[0,1],[0,2],[1,2]]:\n    sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue =  df[ 'SN_filter'].iloc[N0:N1] ) # df['reads'])\n    plt.xlabel(str_inf+str(i))\n    plt.ylabel(str_inf+str(j))\n    \n    plt.show()\n")


# In[57]:


df[ 'SN_filter'].value_counts()


# In[58]:


get_ipython().run_cell_magic('time', '', "from sklearn.decomposition import PCA\n\nfor N0,N1,str_inf1 in [ [0, 821840,'2A4'],[821840, 1643680, 'DMS']]:\n    X = np.clip(df.iloc[N0:N1,33:137].fillna(0),0, 1)\n    str_inf = 'PCA' \n    reducer = PCA(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1],[0,2],[1,2]]:\n        plt.figure(figsize = (20,5))\n        plt.subplot(1,2,1)\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue =  df[ 'SN_filter'].iloc[N0:N1] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1 + ' SN_filter', fontsize = 20 )\n\n        plt.subplot(1,2,2)\n        v = df[  'signal_to_noise' ].iloc[N0:N1].values\n        v = np.clip( v, np.percentile(v,5), np.percentile(v,95)  )\n        v = pd.Series(v); v.name = 'signal_to_noise' \n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue = v  ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1+' signal_to_noise', fontsize = 20 )\n        plt.show()\n")


# In[59]:


get_ipython().run_cell_magic('time', '', "from sklearn.decomposition import PCA\n\n# for N0,N1,str_inf1 in [ [0, 821840,'2A4'],[821840, 1643680, 'DMS']]:\nfor N0,N1,str_inf1 in [ [0, 10_000,'2A4'],[821840, 821840 + 10_000, 'DMS']]:\n    \n    X = np.clip(df.iloc[N0:N1,33:137].fillna(0),0, 1)\n    str_inf = 'PCA' \n    reducer = PCA(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1],[0,2],[1,2]]:\n        plt.figure(figsize = (20,5))\n        plt.subplot(1,2,1)\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue =  df[ 'SN_filter'].iloc[N0:N1] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1 + ' SN_filter', fontsize = 20 )\n\n        plt.subplot(1,2,2)\n        v = df[  'signal_to_noise' ].iloc[N0:N1].values\n        v = np.clip( v, np.percentile(v,5), np.percentile(v,95)  )\n        v = pd.Series(v); v.name = 'signal_to_noise' \n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue = v  ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1+' signal_to_noise', fontsize = 20 )\n        plt.show()\n")


# In[60]:


get_ipython().run_cell_magic('time', '', "from sklearn.decomposition import PCA\nimport umap \n\nfor N0,N1,str_inf1 in [ [0, 10_000,'2A4'],[821840, 821840 + 10_000, 'DMS']]:\n    X = np.clip(df.iloc[N0:N1,33:137].fillna(0),0, 1)\n    str_inf = 'umap' \n#     reducer = PCA(n_components=3)\n    reducer = umap.UMAP(n_components=3)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1],[0,2],[1,2]]:\n        plt.figure(figsize = (20,5))\n        plt.subplot(1,2,1)\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue =  df[ 'SN_filter'].iloc[N0:N1] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1 + ' SN_filter', fontsize = 20 )\n\n        plt.subplot(1,2,2)\n        v = df[  'signal_to_noise' ].iloc[N0:N1].values\n        v = np.clip( v, np.percentile(v,5), np.percentile(v,95)  )\n        v = pd.Series(v); v.name = 'signal_to_noise' \n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue = v  ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1+' signal_to_noise', fontsize = 20 )\n        plt.show()\n")


# In[61]:


get_ipython().run_cell_magic('time', '', "from sklearn.decomposition import PCA\nimport umap \n\nfor N0,N1,str_inf1 in [ [0, 50_000,'2A4'],[821840, 821840 + 50_000, 'DMS']]:\n    X = np.clip(df.iloc[N0:N1,33:137].fillna(0),0, 1)\n    str_inf = 'umap' \n#     reducer = PCA(n_components=3)\n    reducer = umap.UMAP(n_components=2)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1] ]: # ,[0,2],[1,2]]:\n        plt.figure(figsize = (20,5))\n        plt.subplot(1,2,1)\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue =  df[ 'SN_filter'].iloc[N0:N1] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1 + ' SN_filter', fontsize = 20 )\n\n        plt.subplot(1,2,2)\n        v = df[  'signal_to_noise' ].iloc[N0:N1].values\n        v = np.clip( v, np.percentile(v,5), np.percentile(v,95)  )\n        v = pd.Series(v); v.name = 'signal_to_noise' \n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue = v  ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1+' signal_to_noise', fontsize = 20 )\n        plt.show()\n")


# In[62]:


get_ipython().run_cell_magic('time', '', "from sklearn.decomposition import PCA\nimport umap \n\nfor N0,N1,str_inf1 in [ [0, 500_000,'2A4'],[821840, 821840 + 500_000, 'DMS']]:\n    X = np.clip(df.iloc[N0:N1,33:137].fillna(0),0, 1)\n    str_inf = 'umap' \n#     reducer = PCA(n_components=3)\n    reducer = umap.UMAP(n_components=2)\n    Xr = reducer.fit_transform(X)\n    for i,j in [[0,1] ]: # ,[0,2],[1,2]]:\n        plt.figure(figsize = (20,5))\n        plt.suptitle(str(N0)+' - '  +str(N1), fontsize = 20 )\n        plt.subplot(1,2,1)\n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue =  df[ 'SN_filter'].iloc[N0:N1] ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1 + ' SN_filter', fontsize = 20 )\n\n        plt.subplot(1,2,2)\n        v = df[  'signal_to_noise' ].iloc[N0:N1].values\n        v = np.clip( v, np.percentile(v,5), np.percentile(v,95)  )\n        v = pd.Series(v); v.name = 'signal_to_noise' \n        sns.scatterplot(x= Xr[:,i], y = Xr[:,j], hue = v  ) # df['reads'])\n        plt.xlabel(str_inf+str(i))\n        plt.ylabel(str_inf+str(j))\n        plt.title(str_inf1+' signal_to_noise '  , fontsize = 20 )\n        plt.show()\n")


# # Sequence, experiment_type, dataset_name , etc

# In[63]:


len( set(df['sequence']) ), len( set(df['sequence_id']) ), 


# In[64]:


df['experiment_type'].value_counts()


# In[65]:


df['dataset_name'].value_counts().head(5)


# In[66]:


m = df['sequence_id'] == '1728e0d67d7c'
print(m.sum() )
print( df[m]['sequence'].value_counts() )
df[m]


# In[67]:


d = df[['reads','signal_to_noise','SN_filter']].copy()
d['mean targets'] = df.iloc[:,33:137].mean(axis = 1 )
d['median targets'] = df.iloc[:,33:137].median(axis = 1 )
d['std targets'] = df.iloc[:,33:137].std(axis = 1 )
d['var targets'] = df.iloc[:,33:137].var(axis = 1 )



d.corr().round(2)


# In[68]:


df['sequence_id'].value_counts()


# In[69]:


df['sequence_id'].value_counts().value_counts()


# In[70]:


len( set( df['sequence_id'] ) ), df.shape, len(df) - len( set( df['sequence_id'] ) )


# # Targets EDA

# In[71]:


get_ipython().run_cell_magic('time', '', 'for N0,N1, str_info in [ [0,821840,\'2A3\'], [821840,  1643680,\'DMS\']] :\n    #N1 = 821840\n    \n    print( str_info )\n    v = df.iloc[N0:N1,33:136].values.ravel()\n    print(\'Percent Nan:\', np.isnan(v).sum() / len(v)*100 )\n    print(\'Percent == 0 :\', (v==0).sum() / len(v)*100 )\n\n    print(\'Mean:\', np.mean(v[~np.isnan(v)]))\n    print(\'Percent < 0 (bad)\', (v <0).sum()/len(v)*100 )\n    print(\'Percent < 1 (very bad)\', (v <-1).sum()/len(v)*100 )\n    print(\'Percent - "good" - within 0,1:\',  ((v >0)&(v<1)).sum() /len(v) ) \n    print( \'Percent >1 (bad)\', (v >1).sum()/len(v) *100 )\n    print( \'Percent >2 (very bad)\', (v >2).sum()/len(v) *100 )\n    plt.figure(figsize = (20,5))\n    plt.subplot(1,3,1)\n    plt.hist(v.ravel(),bins = 100)\n    plt.title(str_info, fontsize = 20 )\n    plt.subplot(1,3,2)\n    plt.hist(v[ (v>0)&(v<3)] ,bins = 100)\n    plt.title(str_info + \' cut not >0 , <3\', fontsize = 20 )\n    plt.subplot(1,3,3)\n    plt.hist(v[ (v>-1)&(v<2)] ,bins = 100)\n    plt.title(str_info+ \' cut including 0\', fontsize = 20 )\n    \n    plt.show()\n    print()\n')


# In[72]:


get_ipython().run_cell_magic('time', '', "plt.figure(figsize = (20,4) )\nfor N0,N1, str_info in [ [0,821840,'2A3'], [821840,  1643680,'DMS']] :\n    #N1 = 821840\n    \n    print( str_info )\n\n    d = df.iloc[N0:N1,33:137].mean()#.values\n    d.plot(label = str_info)\n    plt.legend()\n    plt.grid()\n    plt.title(str_info + ' targets mean for positions', fontsize = 20  )\nplt.show()\n\nplt.figure(figsize = (20,4) )\nfor N0,N1, str_info in [ [0,821840,'2A3'], [821840,  1643680,'DMS']] :\n    #N1 = 821840\n    \n    print( str_info )\n\n    d = df.iloc[N0:N1,33:137].std()#.values\n    d.plot(label = str_info)\n    plt.legend()\n    plt.grid()\n    plt.title(str_info + ' targets std for positions', fontsize = 20  )\nplt.show()\n")


# # Correlations for Targets
# 
# Seems the first (by index) RNAs in the list - are NOT random, but quite similar
# and we see certain correlations for neigbour-placed targets.
# 
# If we take similar for say indexed 40_000 - 50_000 - that disappears 
# 

# In[73]:


get_ipython().run_cell_magic('time', '', "cm = df.iloc[:1_000,33:137].corr()\nsns.heatmap(cm.abs()[cm.abs()>0.2])\nplt.title('First 1000 - 2A3', fontsize = 20)\nplt.show()\ndisplay( cm.iloc[:10,:10] )\n\ncm = df.iloc[:10_000,33:137].corr()\nsns.heatmap(cm.abs()[cm.abs()>0.2])\nplt.title('First 10_000 - 2A3', fontsize = 20)\nplt.show()\ndisplay( cm.iloc[:10,:10] )\n\ncm = df.iloc[40_000:50_000,33:137].corr()\nsns.heatmap(cm.abs()[cm.abs()>0.2])\nplt.title('40_000:50_000 - 2A3', fontsize = 20)\nplt.show()\ndisplay( cm.iloc[:10,:10] )\n\nN0 = 821840\ncm = df.iloc[N0:N0+10_000,33:137].corr()\nsns.heatmap(cm.abs()[cm.abs()>0.2])\nplt.title('Fist 10_000  - DMS', fontsize = 20)\n\nplt.show()\ndisplay( cm.iloc[:10,:10] )\n")


# # Lengths

# In[74]:


get_ipython().run_cell_magic('time', '', "l = [len(t) for t in df['sequence']]\nprint( pd.Series(l).describe() )\n\nplt.hist(l, bins = 100)\nplt.show()\npd.Series(l).value_counts()\n")


# In[75]:


1568354/len(df)*100


# # Levenshtein distances 

# In[76]:


from Levenshtein import distance
edit_dist = distance("ah", "aho")
edit_dist


# In[77]:


get_ipython().run_cell_magic('time', '', "i = 0\ns = df['sequence'].iat[i]\nl = [distance(s,t) for t in df['sequence']]\nprint( pd.Series(l).describe() )\n\nplt.hist(l, bins = 100)\nplt.show()\npd.Series(l).value_counts()\n")


# In[78]:


m = pd.Series(l) < 40
print(m.sum() )
plt.plot(df[m].index)
df[m]


# In[79]:


get_ipython().run_cell_magic('time', '', "for j in range(10):\n    i = 10_000*j\n    s = df['sequence'].iat[i]\n    l = [distance(s,t) for t in df['sequence']]\n    sr = pd.Series(l)\n    print()\n    print(i, 'Count, percent dist < 40 : ' , (sr<40).sum() , (sr<40).sum() / len(sr)*100 ) \n    print()\n    print( sr.describe() )\n    print(sr.value_counts().head(3) )\n    \n    plt.hist(l, bins = 100)\n    plt.show()\n")


# In[80]:


get_ipython().run_cell_magic('time', '', "#for j in range(10):\ni = 10_000*1\ns = df['sequence'].iat[i]\nl = [distance(s,t) for t in df['sequence']]\nsr = pd.Series(l)\nprint()\nprint(i, 'Count, percent dist < 40 : ' , (sr<40).sum() , (sr<40).sum() / len(sr)*100 ) \nprint()\nprint( sr.describe() )\nprint(sr.value_counts().head(3) )\n\nplt.hist(l, bins = 100)\nplt.show()\n")


# In[81]:


get_ipython().run_cell_magic('time', '', 'm = sr<40\nprint( m.sum() )\nv = df[m].iloc[:,33:137].std()\n# v = np.clip(df[m].iloc[:,33:137],0,1).std()\nplt.plot(v)\n\n# v = np.clip(df.iloc[:,33:137],0,1).std()\nv = df.iloc[:,33:137].std()\nplt.plot(v)\n\nplt.show()\n')


# In[82]:


get_ipython().run_cell_magic('time', '', 'm = sr<40\nprint( m.sum() )\n# v = df[m].iloc[:,33:137].std()\nv = np.clip(df[m].iloc[:,33:137],0,1).std()\nplt.plot(v)\n\nv = np.clip(df.iloc[:,33:137],0,1).std()\nplt.plot(v)\n\nplt.show()\n')


# In[83]:


np.sort(l)[2:60]


# # Description
# 
# Welcome to Stanford Ribonanza RNA Folding challenge. The task of this competition is predicting the chemical reactivity at each position of an RNA molecule. These data are extremely sensitive to the structure that each RNA forms, and an algorithm that could perfectly predict these chemical reactivities would need to have an implicit ‘understanding’ of RNA structure. Such an oracle could be then utilized to predictively model structures of novel RNA molecules. A better understanding of how to manipulate RNA could help usher in an age of programmable medicine, including first cures for pancreatic cancer and Alzheimer’s disease as well as much-needed antibiotics and new biotechnology approaches for climate change. 
# 
# This notebook provides a simple baseline that may be used as a starting point for further experiments. Improvment of the baseline may include: 
# 1. Use of proper loss function to incorporate SN_filter = 0 samples as well as reactivity errors into training
# 2. Model improvement and use of additional data, e.g. Ribonanza_bpp_files
# 
# Finally, working on this competition keep in mind that train/public LB have different sequence length distribution from private LB, i.e. 115-206 vs. 207-457. Therefore, to avoid a strong shakeup one may need to look into performance vs. sequence length end ensure generalizability.

# # Data
# 
# The primary training data is provided in train_data.csv, which contains 821840 RNA sequences and the corresponding reactivity measurements with 2A3_MaP and DMS_MaP methods. The reactivity is reported in columns reactivity_0001 - reactivity_0206 and is set to NaN for the first 26 and the last 21 nucleotides as well as padding for sequences shorter than 206. For faster loading and effective RAM use, I converted the data into a float32 parquet file. 
# 
# Evaluation in this competition is performed only on samples with SN_filter = 1 for both measurement methods. In this example, I perform training only on samples wiht SN_filter = 1, which gives a noticeable CV boost but uses only 1/4 of the data (i.e. training on noisy SN_filter = 0 data degrades the performance). A proper consideration of all data as well as reactivity errors may boost the performance.
# 
# In this example, I use a simple CV Kfold split. However, given a mismatch in the RNA length between train/public LB vs. private LB data, **it may be important to verify the effect of the sequence length** to avoid a significant shakeup at the private LB.
# 
# One of the tricks, well known in NLP community, which I use here, is length matching batch sampling: composing batches of samples of approximately the same length to minimize the overhead caused by padding tokens.

#     About 3hours with GPU
#     
#     epoch	train_loss	valid_loss	mae	time
#     0	0.259645	0.242505	0.242824	05:32
#     1	0.232985	0.239556	0.240022	05:17
#     2	0.224673	0.230146	0.230736	05:17
#     3	0.217918	0.224602	0.225077	05:17
#     4	0.211552	0.214193	0.214585	05:17
#     5	0.203101	0.208112	0.208401	05:16
#     6	0.197911	0.199414	0.199593	05:17
#     7	0.193171	0.193157	0.193358	05:17
#     8	0.189614	0.188340	0.188529	05:17
#     9	0.186561	0.187671	0.187903	05:17
#     10	0.183876	0.183966	0.184206	05:16
#     11	0.181590	0.182045	0.182316	05:17
#     12	0.180300	0.180280	0.180582	05:17
#     13	0.177671	0.177043	0.177349	05:17
#     14	0.175484	0.174906	0.175268	05:17
#     15	0.174364	0.172992	0.173380	05:17
#     16	0.173053	0.171768	0.172208	05:17
#     17	0.171538	0.169716	0.170161	05:17
#     18	0.169739	0.168699	0.169170	05:17
#     19	0.169160	0.168604	0.169072	05:17
#     20	0.167819	0.166619	0.167131	05:17
#     21	0.167098	0.165825	0.166352	05:17
#     22	0.165852	0.164539	0.165069	05:17
#     23	0.165208	0.164545	0.165073	05:17
#     24	0.164834	0.163786	0.164341	05:17
#     25	0.164269	0.163232	0.163790	05:17
#     26	0.163931	0.163240	0.163799	05:17
#     27	0.163718	0.162777	0.163343	05:18
#     28	0.164019	0.162552	0.163110	05:17
#     29	0.163497	0.162560	0.163127	05:17
#     30	0.163550	0.162594	0.163162	05:17
#     31	0.163162	0.162560	0.163128	05:17
# 

# In[84]:


# Broadcast: 
# A = np.zeros( (2,3) )
# b = np.ones(3)
# b
# A+b[np.newaxis,:]


# # Submission prepare by column mean
# 
# 

# In[85]:


get_ipython().run_cell_magic('time', '', "l = [len(t) for t in dft['sequence']] \nprint( pd.Series(l).value_counts() )\n\nfor k in range(len(l)-1):\n    if l[k]!=l[k+1]:\n        print(k,l[k],l[k+1])\n")


# In[86]:


get_ipython().run_cell_magic('time', '', "\nll = ['reactivity_'+'%04d'%i for i in range(1,178)]\nprint(ll[:2])\n\nm = df['SN_filter'] == 1\nm1 = df['experiment_type'] == '2A3_MaP'\nv  = df[m&m1][ll].median(axis = 0).fillna(0)\nv[v<0] = 0\nv[v>0.35] = 0.35\nv = v.round(3)\nprint(list(np.round(v.values,3)))\nplt.figure(figsize = (20,4))\nplt.plot(v.values,'*-')\nplt.title('2A3',fontsize = 20)\nplt.show()\n\nv = np.round(v.values, 3)\nY_2A3_pred = np.zeros( (335823, 177), dtype = np.float16 )\nY_2A3_pred += v[np.newaxis,:]\nY_2A3_pred[:3,25:28]\n")


# In[87]:


get_ipython().run_cell_magic('time', '', "\nll = ['reactivity_'+'%04d'%i for i in range(1,178)]\nprint(ll[:2])\n\nm = df['SN_filter'] == 1\nm1 = df['experiment_type'] == 'DMS_MaP'\nv  = df[m&m1][ll].median(axis = 0).fillna(0)\nv[v<0] = 0\nv[v>0.35] = 0.35\nv = v.round(3)\nprint(list(np.round(v.values,3)))\nplt.figure(figsize = (20,4))\nplt.plot(v.values,'*-')\nplt.title('2A3',fontsize = 20)\nplt.show()\n\nv = np.round(v.values, 3)\nY_DMS_pred = np.zeros( (335823, 177) , dtype = np.float16)\nY_DMS_pred += v[np.newaxis,:]\nY_DMS_pred[:3,25:28]\n")


# In[88]:


get_ipython().run_cell_magic('time', '', 'N = 269796671\ndf_submit = pd.DataFrame(index = range(N) )\n')


# In[89]:


get_ipython().run_cell_magic('time', '', "df_submit.index.name = 'id'\ndf_submit['reactivity_DMS_MaP'] = np.zeros( N, dtype = np.float16 )\ndf_submit['reactivity_2A3_MaP'] = np.zeros( N, dtype = np.float16 )\nK = len(Y_DMS_pred.ravel() )\ndf_submit['reactivity_DMS_MaP'].iloc[:K] = Y_DMS_pred.ravel()\ndf_submit['reactivity_2A3_MaP'].iloc[:K] = Y_2A3_pred.ravel()\n")


# In[90]:


df_submit.iloc[30:35,:]


# In[91]:


get_ipython().run_cell_magic('time', '', "df_submit.to_csv('submission.csv',float_format='%.3f')\n")


# In[ ]:





# In[ ]:





# In[ ]:





# In[92]:


print('%.1f seconds passed total '%(time.time()-t0start) )
print('%.1f minutes passed total '%( (time.time()-t0start)/60)  )
print('%.2f hours passed total '%( (time.time()-t0start)/3600)  )


# In[ ]:




