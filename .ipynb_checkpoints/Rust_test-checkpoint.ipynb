{"cells": [{"cell_type": "code", "execution_count": null, "id": "5b2d3cd5-10ee-4204-bd2c-0bc658e64a48", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "46ee196c-7381-41eb-a492-ed28ce8fe944", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "2d6fc3c4-1d9b-4b99-9a87-c788427913d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello from Conda!\n"]}], "source": ["println!(\"Hello from Conda!\");\n"]}, {"cell_type": "code", "execution_count": null, "id": "bc06e2a0-891e-42b0-98c1-4a2eb8f1a049", "metadata": {"scrolled": true}, "outputs": [], "source": ["// !echo $CONDA_PREFIX\n", "// !jupyter kernelspec list\n", "// !which python\n", "// import os\n", "// print(os.environ.get(\"CONDA_PREFIX\"))\n", "// import sys\n", "// print(sys.executable)\n", "// import subprocess\n", "// # Instead of !which python\n", "// result = subprocess.run(['which', 'python'], capture_output=True, text=True)\n", "// print(result.stdout)\n", "// # import torch\n", "// # torch.cuda.is_available()"]}, {"cell_type": "code", "execution_count": null, "id": "e6cd42a9-6a96-491a-8ec0-75db69ca9d52", "metadata": {}, "outputs": [], "source": ["// # // Add required dependencies first\n", ":dep ndarray = \"0.15\"\n", ":dep ndarray-stats = \"0.5\" \n", ":dep statrs = \"0.16\"\n", ":dep plotters = { version = \"0.3.5\", features = [\"evcxr\"] }\n", "\n", "// Import with correct modules\n", "use ndarray::prelude::*;\n", "use ndarray_stats::QuantileExt;  // For axis operations\n", "use statrs::statistics::Statistics;\n"]}, {"cell_type": "code", "execution_count": null, "id": "a06daf6f-505e-4209-80f3-dee20665e8db", "metadata": {}, "outputs": [{"ename": "Error", "evalue": "unresolved import `plotly`", "output_type": "error", "traceback": ["\u001b[31m[E0432] Error:\u001b[0m unresolved import `plotly`"]}, {"ename": "Error", "evalue": "unresolved import `plotly`", "output_type": "error", "traceback": ["\u001b[31m[E0432] Error:\u001b[0m unresolved import `plotly`"]}, {"ename": "Error", "evalue": "cannot find macro `array` in this scope", "output_type": "error", "traceback": ["\u001b[31mError:\u001b[0m cannot find macro `array` in this scope", "   \u001b[38;5;246m╭\u001b[0m\u001b[38;5;246m─\u001b[0m\u001b[38;5;246m[\u001b[0mcommand_4:1:1\u001b[38;5;246m]\u001b[0m", "   \u001b[38;5;246m│\u001b[0m", " \u001b[38;5;246m4 │\u001b[0m \u001b[38;5;249ml\u001b[0m\u001b[38;5;249me\u001b[0m\u001b[38;5;249mt\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;249mx\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;249m=\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;54ma\u001b[0m\u001b[38;5;54mr\u001b[0m\u001b[38;5;54mr\u001b[0m\u001b[38;5;54ma\u001b[0m\u001b[38;5;54my\u001b[0m\u001b[38;5;249m!\u001b[0m\u001b[38;5;249m[\u001b[0m\u001b[38;5;249m1\u001b[0m\u001b[38;5;249m,\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;249m2\u001b[0m\u001b[38;5;249m,\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;249m3\u001b[0m\u001b[38;5;249m,\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;249m4\u001b[0m\u001b[38;5;249m,\u001b[0m\u001b[38;5;249m \u001b[0m\u001b[38;5;249m5\u001b[0m\u001b[38;5;249m]\u001b[0m\u001b[38;5;249m;\u001b[0m", " \u001b[38;5;240m  │\u001b[0m         \u001b[38;5;54m─\u001b[0m\u001b[38;5;54m─\u001b[0m\u001b[38;5;54m┬\u001b[0m\u001b[38;5;54m─\u001b[0m\u001b[38;5;54m─\u001b[0m  ", " \u001b[38;5;240m  │\u001b[0m           \u001b[38;5;54m╰\u001b[0m\u001b[38;5;54m─\u001b[0m\u001b[38;5;54m─\u001b[0m\u001b[38;5;54m─\u001b[0m\u001b[38;5;54m─\u001b[0m error: cannot find macro `array` in this scope", "\u001b[38;5;246m───╯\u001b[0m"]}], "source": ["// Simple Plotting (requires evcxr_jupyter)\n", "use plotly::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>};\n", "\n", "let x = array![1, 2, 3, 4, 5];\n", "let y = x.mapv(|v| v.powf(1.5));\n", "let trace = Scatter::new(x.to_vec(), y.to_vec());\n", "let mut plot = Plot::new();\n", "plot.add_trace(trace);\n", "plot.show();\n"]}, {"cell_type": "code", "execution_count": null, "id": "d883768e-dee1-45db-a3a3-6c424699c07b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d5a808aa-7bd5-4f93-8234-dc9399c89deb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4b866a1e-3b41-4f11-befd-0534004e5891", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8f3864fa-4099-4568-ae18-437c8faff0c2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22793541-feb2-4f0b-9107-53350e7f61c2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c33af82-1b9a-4868-9218-ff5e2ee6cfab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f821e70d-1d76-4d23-ab86-0049dcd8236f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c3f43e40-cf92-49ec-bdb5-05d70db2d468", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7a5cfcc2-3bd0-42ee-affa-d9d1b02f1fc2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Rust [conda env:kaggle]", "language": "rust", "name": "conda-env-kaggle-rust"}, "language_info": {"codemirror_mode": "rust", "file_extension": ".rs", "mimetype": "text/rust", "name": "Rust", "pygment_lexer": "rust", "version": ""}}, "nbformat": 4, "nbformat_minor": 5}