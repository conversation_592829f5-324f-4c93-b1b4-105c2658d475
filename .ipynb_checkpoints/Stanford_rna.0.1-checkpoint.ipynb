{"cells": [{"cell_type": "code", "execution_count": null, "id": "1647299d-188d-4d4e-aa8a-3d7eb545ed7d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e62c7b53-c642-44ba-8a73-4394786766b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0cab4f29-4c3c-47e6-9a58-10aaea9443c1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "20cb7b55-60d7-47f0-a771-04888932e518", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/copick\n", "Available kernels:\n", "  python3        /home/<USER>/miniconda3/envs/copick/share/jupyter/kernels/python3\n", "  copick         /home/<USER>/.local/share/jupyter/kernels/copick\n", "  copick1        /home/<USER>/.local/share/jupyter/kernels/copick1\n", "  copick3unet    /home/<USER>/.local/share/jupyter/kernels/copick3unet\n", "  cuda121        /home/<USER>/.local/share/jupyter/kernels/cuda121\n", "  cudabase       /home/<USER>/.local/share/jupyter/kernels/cudabase\n", "  dacon          /home/<USER>/.local/share/jupyter/kernels/dacon\n", "  graph          /home/<USER>/.local/share/jupyter/kernels/graph\n", "  graph1         /home/<USER>/.local/share/jupyter/kernels/graph1\n", "  graph2         /home/<USER>/.local/share/jupyter/kernels/graph2\n", "  kaggle         /home/<USER>/.local/share/jupyter/kernels/kaggle\n", "  py310          /home/<USER>/.local/share/jupyter/kernels/py310\n", "  rust           /home/<USER>/.local/share/jupyter/kernels/rust\n", "  stumpy         /home/<USER>/.local/share/jupyter/kernels/stumpy\n", "  tabr           /home/<USER>/.local/share/jupyter/kernels/tabr\n", "  xsmiles_env    /home/<USER>/.local/share/jupyter/kernels/xsmiles_env\n", "/home/<USER>/miniconda3/envs/copick/bin/python\n", "/home/<USER>/miniconda3/envs/copick\n", "/home/<USER>/miniconda3/envs/copick/bin/python\n", "/home/<USER>/miniconda3/envs/copick/bin/python\n", "\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["!echo $CONDA_PREFIX\n", "!jupyter kernelspec list\n", "!which python\n", "import os\n", "print(os.environ.get(\"CONDA_PREFIX\"))\n", "import sys\n", "print(sys.executable)\n", "import subprocess\n", "# Instead of !which python\n", "result = subprocess.run(['which', 'python'], capture_output=True, text=True)\n", "print(result.stdout)\n", "import torch\n", "torch.cuda.is_available()"]}, {"cell_type": "code", "execution_count": null, "id": "e94295c6-e4c9-4608-8569-309ba37b28ee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "dcea92d5-4281-4e14-a75e-8e7e1851dacd", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ID: query\n", "Sequence: GGGAGUGAAGGAGGCUCGCGAACUCGCGAAGCCGAGAAACUUCACUCCC\n", "ID: 2VPL_B_1_97_f/1-48\n", "Sequence: -GGAGUGAAGGAGGCUCGCGAACUCGCGAAGCCGAGAAACUUCACUCCC\n", "ID: LR792632.1_105593_105734_r/51-91\n", "Sequence: ----GUGAAGGAGGCUCGCGAACUCGCGAAGCCGAGAAACUUCAC----\n", "ID: CP001787.1_1598350_1598491_r/51-91\n", "Sequence: ----GUGAAGGAGGCUCGCGAACUCGCGAAGCCGAGGAACUUCAC----\n", "ID: CP002737.1_948142_948286_f/52-92\n", "Sequence: ----GUGAAGGAGGCUCGCCGCCUUGCGAAGCCGCGAAACUUCAC----\n", "ID: CP002009.1_909371_909511_f/52-98\n", "Sequence: CUGU--GAAGGAGGCUCGCCAACUUGCGAAGCCGAGAAACUUUGUACAA\n", "ID: 1ZHO_B_1_87_f/1-38\n", "Sequence: GGGAGUGAAGGAGGC---------------GCCGCGAAACUUCACUCCC\n", "ID: CP011266.1_464503_464645_f/48-96\n", "Sequence: UGCGGUGAAGGAGGCUAAAGAACAAUCUAAGCCGAGAAACUUCACUGAG\n", "ID: AP011526.1_261954_262097_r/51-96\n", "Sequence: GC-AGUGAAGGAGGCUCGAACUCUUGCUAAGCCGCGAAACUUCAC--GC\n", "ID: CP002792.1_16676_16820_f/52-92\n", "Sequence: ----GUGAAGGAGGCUCGUUCUCUUGCUAAGCCGCGAAACUUCAC----\n", "ID: AF044919.1_432_576_f/52-92\n", "Sequence: ----GUGAAGGAGGCUCGUUCUCUCGCAAAGCCGCGAAACUUCAC----\n", "ID: X16023.1_1052_1196_f/52-92\n", "Sequence: ----GUGAAGGAGGCU--CGAACUCGCUAAGCCGCGAAACUUCAC----\n", "ID: HG995296.1_6522815_6522966_r/50-103\n", "Sequence: GAGCGUGAAGGAG---CGUGA---UGCGAGAGCGGGAAACUUCACGCUC\n", "ID: URS000080E2CF_1_85_f/1-36\n", "Sequence: GGG-GUGAAGGAGGC---------------GCCGCGAAACUUCAC-CCC\n", "ID: CP000743.1_206214_206356_r/50-97\n", "Sequence: UGCAGUGAAGGAGGCUCGUAAAAAUGCUAAGCCGCGAAACUUCAC--CA\n", "ID: CP010834.1_361855_361997_f/48-96\n", "Sequence: CGCGGUGAAGGAGGC---------------GCCGAGAAACUUCACUGAG\n", "ID: CP004050.1_314560_314702_f/48-96\n", "Sequence: UGCGGUGAAGAAGGCUAAAGAAC-----AAGCCGAGGAACUUCACUGAG\n", "ID: AF139164.1_250_396_f/54-94\n", "Sequence: ----GUGAAGGAGGCUCG--------CAAAGCCGCGAAACUUCAC----\n", "ID: CP001719.1_670700_670845_f/50-97\n", "Sequence: UGCGGUGAAGGAGGC---------------GCCGAGAAACUUCACU-CA\n", "ID: CP001710.1_257903_258044_f/51-91\n", "Sequence: ----GUGAAGAAGGCCAAGGAACUUUCAAGGCCGAGAAACUUCAC----\n", "ID: OU538833.1_1668960_1669107_r/50-99\n", "Sequence: GACAGUGACGGAGGCU------------AACCCGCGAAACGUCACUGAC\n", "ID: CP006933.1_1858955_1859096_f/48-95\n", "Sequence: UCG-GUGAAGAAGGCU------------AAGCCGAGAAACUUCACACAA\n", "ID: HG425166.1_433504_433645_r/51-91\n", "Sequence: ----GUGAAGAAGGCU------------AAGCCGAGAAACUUCAC----\n", "ID: CP006019.1_386166_386309_f/50-95\n", "Sequence: GCG-GUGAAGGAGGCU------------AAGCCGCGUAACUUCAC--GC\n", "ID: CP006670.1_485077_485218_r/51-91\n", "Sequence: ----GUGAAGGAGGCU------------AAGCCGCGUAACUUCAC----\n", "ID: CP002772.1_436798_436939_r/51-91\n", "Sequence: ----GUGAAGAAGGCUAAAGAAGACUCAAAGCCGAGGAACUUCAC----\n", "ID: CP044013.1_194640_194781_f/51-91\n", "Sequence: ----GUGAAGAAGGCCAAGGAACUGUCAAGGCCGAGAAACUUCAC----\n", "ID: AP025586.1_527974_528115_r/51-91\n", "Sequence: ----GUGAAGGAGGC---------------GCCGAGAAACUUCAC----\n", "ID: CP036261.1_4341031_4341167_r/50-88\n", "Sequence: GGGAGUGAAGGA---------------------------CUUCACUCCC\n", "ID: CP002551.1_2282982_2283123_f/51-91\n", "Sequence: ----GUGAAGAAGGCU------------AAGCCGAGGAACUUCAC----\n", "ID: CP014213.1_2498095_2498239_f/52-92\n", "Sequence: ----GUGAAGAAGGUUU----------AAAACCGAGAAACUUCAC----\n", "ID: LR698975.1_1431852_1431996_r/52-92\n", "Sequence: ----GUGAAGAAGGUU------------AAACCGAGAAACUUCAC----\n", "ID: AP025698.1_1293055_1293196_f/51-91\n", "Sequence: ----GUGAAGAAGGCUAAGGAAC--C--AAGCCGCGGAACUUCAC----\n", "ID: AP017647.1_1436412_1436553_f/51-91\n", "Sequence: ----GUGAAGAAGGCU------------AAGCCGCGGAACUUCAC----\n", "ID: CP022705.1_1412684_1412825_f/51-91\n", "Sequence: ----GUGAAGAAGGCC-------------AGCCGAGAAACUUCAC----\n", "ID: AP019779.1_1035556_1035697_r/51-91\n", "Sequence: ----GUGAAGAAGGC---------------GCCGAGAAACUUCAC----\n", "ID: HG995296.1_6522815_6522966_r/103-60\n", "Sequence: GAGCGUGAAGUU---------------------GGGAAACUUCACGCUC\n", "ID: CP065989.1_1090329_1090458_f/50-76\n", "Sequence: ------------GGCCCGCGAACUCGCGAACCCGAGGAA----------\n", "ID: 1ZHO_B_1_87_f/38-1\n", "Sequence: GGGAGUGAAG-----------------------------CUUCACUCCC\n", "ID: 1U63_D_1_98_f/49-1\n", "Sequence: GGGAGUGAAG--GGCUCGCGAGUUCGCG-AGCCGA----CUUCACUCCC\n", "ID: OX276342.1_6776854_6776976_r/60-74\n", "Sequence: ---------------UCGCGAACUCGCGAA-------------------\n", "ID: 2VPL_B_1_97_f/48-1\n", "Sequence: GGGAGUGAAG--GGCUCGCGAGUUCGCG-AGCCUC----CUUCACUCC-\n", "ID: URS000080E2CF_1_85_f/36-1\n", "Sequence: GGG-GUGAAG-----------------------------CUUCAC-CCC\n"]}], "source": ["from Bio import AlignIO\n", "\n", "# Replace 'your_file.fasta' with the path to your FASTA file\n", "alignment = AlignIO.read(\"/app/Kaggle/StanfordRNA/MSA/1U63_D.MSA.fasta\", \"fasta\")\n", "\n", "# Iterate through the sequences in the alignment\n", "for record in alignment:\n", "    print(f\"ID: {record.id}\")\n", "    print(f\"Sequence: {record.seq}\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "ee0c6b4a-5790-4de8-9c8f-f7f0c567f243", "metadata": {}, "outputs": [{"data": {"text/plain": ["<<class 'Bio.Align.MultipleSeqAlignment'> instance (43 records of length 49) at 7f2d3ba5ff20>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["alignment"]}, {"cell_type": "code", "execution_count": 4, "id": "29275e48-dde3-4fd1-823e-4093ec9a9dae", "metadata": {}, "outputs": [], "source": ["from RNA import RNA\n"]}, {"cell_type": "code", "execution_count": 5, "id": "fc28725f-3209-48f0-9fd2-55c88e046b39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample submission (first few rows):\n", "         ID resname  resid       x_1       y_1  z_1       x_2       y_2  z_2  \\\n", "0  1SCL_A_1       G      1  2.000000  0.000000  0.0  2.000000  0.000000  0.0   \n", "1  1SCL_A_2       G      2  1.755165  0.958851  0.1  1.755165  0.958851  0.1   \n", "2  1SCL_A_3       G      3  1.080605  1.682942  0.2  1.080605  1.682942  0.2   \n", "3  1SCL_A_4       U      4  0.141474  1.994990  0.3  0.141474  1.994990  0.3   \n", "4  1SCL_A_5       G      5 -0.832294  1.818595  0.4 -0.832294  1.818595  0.4   \n", "\n", "        x_3       y_3  z_3       x_4       y_4  z_4       x_5       y_5  z_5  \n", "0  2.000000  0.000000  0.0  2.000000  0.000000  0.0  2.000000  0.000000  0.0  \n", "1  1.755165  0.958851  0.1  1.755165  0.958851  0.1  1.755165  0.958851  0.1  \n", "2  1.080605  1.682942  0.2  1.080605  1.682942  0.2  1.080605  1.682942  0.2  \n", "3  0.141474  1.994990  0.3  0.141474  1.994990  0.3  0.141474  1.994990  0.3  \n", "4 -0.832294  1.818595  0.4 -0.832294  1.818595  0.4 -0.832294  1.818595  0.4  \n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from ViennaRNA import RNA\n", "import os\n", "\n", "# Load sample data (replace with actual file paths when available)\n", "def load_data():\n", "    # Sample train_sequences based on your input\n", "    train_sequences = pd.DataFrame({\n", "        'target_id': ['1SCL_A', '1RNK_A'],\n", "        'sequence': ['GGGUGCUCAGUACGAGAGGAACCGCACCC', 'GGCGCAGUGGGCUAGCGCCACUCAAAAGGCCCAU'],\n", "        'temporal_cutoff': ['1995-01-26', '1995-02-27'],\n", "        'description': ['THE SARCIN-RICIN LOOP, A MODULAR RNA', 'THE STRUCTURE OF AN RNA PSEUDOKNOT'],\n", "        'all_sequences': ['>1SCL_1|Chain A|RNA SARCIN-RICIN LOOP|Rattus n...', '>1RNK_1|Chain A|RNA PSEUDOKNOT|null\\nGGCGCAGUG...']\n", "    })\n", "    # Sample train_labels\n", "    train_labels = pd.DataFrame({\n", "        'ID': ['1SCL_A_1', '1SCL_A_2', '1SCL_A_3', '1RNK_A_1', '1RNK_A_2'],\n", "        'resname': ['G', 'G', 'G', 'G', 'G'],\n", "        'resid': [1, 2, 3, 1, 2],\n", "        'x_1': [13.760, 9.310, 5.529, 10.0, 11.0],\n", "        'y_1': [-25.974001, -29.638000, -27.813000, -20.0, -21.0],\n", "        'z_1': [0.102, 2.669, 5.878, 0.0, 1.0]\n", "    })\n", "    return train_sequences, train_labels\n", "\n", "# Predict multiple secondary structures using RNAfold\n", "def predict_secondary_structures(sequence, num_structures=5):\n", "    RNA.cvar.temperature = 37.0\n", "    fc = RNA.fold_compound(sequence)\n", "    fc.pf()  # Compute partition function\n", "    \n", "    # Get MFE structure\n", "    (mfe_structure, mfe_energy) = fc.mfe()\n", "    structures = [(mfe_structure, mfe_energy)]\n", "    \n", "    # Get suboptimal structures\n", "    energy_range = 5.0  # Explore structures within 5 kcal/mol of MFE\n", "    subopt_structures = fc.subopt(int(energy_range * 100))  # Returns list of suboptimal structures\n", "    \n", "    for subopt in subopt_structures[:num_structures - 1]:\n", "        structures.append((subopt.structure, subopt.energy))\n", "    \n", "    # Pad with MFE if fewer than num_structures\n", "    while len(structures) < num_structures:\n", "        structures.append((mfe_structure, mfe_energy))\n", "    \n", "    return [s[0] for s in structures[:num_structures]]  # Return dot-bracket notations\n", "\n", "# Simplified 3D coordinate generation (placeholder for RNAComposer/Rosetta)\n", "def generate_3d_coordinates(sequence, sec_structure):\n", "    length = len(sequence)\n", "    coords = np.zeros((length, 3))\n", "    for i in range(length):\n", "        # Dummy helical model: adjust based on secondary structure\n", "        angle = i * 0.5\n", "        z_offset = i * 0.1\n", "        if sec_structure[i] in '()':  # Paired nucleotide\n", "            coords[i] = [np.cos(angle) * 2, np.sin(angle) * 2, z_offset]\n", "        else:  # Unpaired\n", "            coords[i] = [np.cos(angle) * 3, np.sin(angle) * 3, z_offset]\n", "    return coords\n", "\n", "# Predict five structures per sequence\n", "def predict_rna_structures(sequences_df):\n", "    predictions = []\n", "    for _, row in sequences_df.iterrows():\n", "        target_id = row['target_id']\n", "        sequence = row['sequence']\n", "        \n", "        # Get five secondary structures\n", "        sec_structures = predict_secondary_structures(sequence)\n", "        \n", "        # Generate 3D coordinates for each\n", "        all_coords = [generate_3d_coordinates(sequence, ss) for ss in sec_structures]\n", "        \n", "        # Format output for each nucleotide\n", "        for resid, nucleotide in enumerate(sequence, 1):\n", "            row_data = {'ID': f\"{target_id}_{resid}\", 'resname': nucleotide, 'resid': resid}\n", "            for i, coords in enumerate(all_coords, 1):\n", "                row_data[f'x_{i}'] = coords[resid-1][0]\n", "                row_data[f'y_{i}'] = coords[resid-1][1]\n", "                row_data[f'z_{i}'] = coords[resid-1][2]\n", "            predictions.append(row_data)\n", "    \n", "    return pd.<PERSON><PERSON><PERSON><PERSON>(predictions)\n", "\n", "# Main execution\n", "# if __name__ == \"__main__\":\n", "    # Load data\n", "train_sequences, train_labels = load_data()\n", "\n", "# Predict structures (using train_sequences as a test proxy)\n", "submission_df = predict_rna_structures(train_sequences)\n", "\n", "# Save to CSV\n", "submission_df.to_csv('submission.csv', index=False)\n", "print(\"Sam<PERSON> submission (first few rows):\")\n", "print(submission_df.head())"]}, {"cell_type": "code", "execution_count": null, "id": "31c0453c-b35d-4eb0-b768-363f39f6545e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab739d56-04f8-4926-959a-ef6409cae59b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c839fa8c-6907-4386-9fe1-871d27a0e7d2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "train_sequences = pd.read_csv('train_sequences.v2.csv')\n", "test_sequences = pd.read_csv('test_sequences.csv')\n", "train_labels = pd.read_csv('train_labels.v2.csv')\n", "validation_labels = pd.read_csv('validation_labels.csv')\n", "validation_sequences = pd.read_csv('validation_sequences.csv')\n", "sample_submission = pd.read_csv('sample_submission.csv')\n"]}, {"cell_type": "code", "execution_count": null, "id": "32445b0d-0fbd-44ce-9d2c-06806c013ae6", "metadata": {"scrolled": true}, "outputs": [], "source": ["# for seq in range(0,len(train_sequences)):\n", "#     print(len(train_sequences.sequence[seq]))\n", "train_sequences"]}, {"cell_type": "code", "execution_count": 13, "id": "a1476480-63ae-428c-a22c-b4338e423f07", "metadata": {}, "outputs": [{"data": {"text/plain": ["5134"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["seq\n"]}, {"cell_type": "code", "execution_count": 21, "id": "31dbdf7b-049e-466f-874f-7d1d001ff801", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing R1107 (length 69)...\n", "Converted /app/Kaggle/StanfordRNA/MSA/R1107.MSA.fasta to /app/Kaggle/StanfordRNA/output/R1107.a3m using Python\n", "predict.py\n", "predict.py output: predict SS by SPOT-RNA\n", "predict geometries\n", "done!\n", "saving......\n", "\n", "temp_dir /app/Kaggle/StanfordRNA/output/temp_R1107\n", "fold.py output: temp folder:      /app/Kaggle/StanfordRNA/output/temp_R1107\n", "┌──────────────────────────────────────────────────────────────────────────────┐\n", "│                                 PyRosetta-4                                  │\n", "│              Created in JHU by <PERSON> and PyRosetta Team              │\n", "│              (C) Copyright Rosetta Commons Member Institutions               │\n", "│                                                                              │\n", "│ NOTE: USE OF PyRosetta FOR COMMERCIAL PURPOSES REQUIRE PURCHASE OF A LICENSE │\n", "│         See LICENSE.PyRosetta.md <NAME_EMAIL> for details         │\n", "└──────────────────────────────────────────────────────────────────────────────┘\n", "PyRosetta-4 2025 [Rosetta PyRosetta4.conda.ubuntu.cxx11thread.serialization.Ubuntu.python312.Release 2025.17+release.356248d2035a0749e09a4a79479678a8f54e7220 2025-04-17T21:51:51] retrieved from: http://www.pyrosetta.org\n", "\n", "done\n", "eRMSD = 0.10\n", "\n", "Processing R1108 (length 69)...\n", "Converted /app/Kaggle/StanfordRNA/MSA/R1108.MSA.fasta to /app/Kaggle/StanfordRNA/output/R1108.a3m using Python\n", "predict.py\n", "predict.py output: predict SS by SPOT-RNA\n", "predict geometries\n", "done!\n", "saving......\n", "\n", "temp_dir /app/Kaggle/StanfordRNA/output/temp_R1108\n", "fold.py output: temp folder:      /app/Kaggle/StanfordRNA/output/temp_R1108\n", "┌──────────────────────────────────────────────────────────────────────────────┐\n", "│                                 PyRosetta-4                                  │\n", "│              Created in JHU by <PERSON> and PyRosetta Team              │\n", "│              (C) Copyright Rosetta Commons Member Institutions               │\n", "│                                                                              │\n", "│ NOTE: USE OF PyRosetta FOR COMMERCIAL PURPOSES REQUIRE PURCHASE OF A LICENSE │\n", "│         See LICENSE.PyRosetta.md <NAME_EMAIL> for details         │\n", "└──────────────────────────────────────────────────────────────────────────────┘\n", "PyRosetta-4 2025 [Rosetta PyRosetta4.conda.ubuntu.cxx11thread.serialization.Ubuntu.python312.Release 2025.17+release.356248d2035a0749e09a4a79479678a8f54e7220 2025-04-17T21:51:51] retrieved from: http://www.pyrosetta.org\n", "\n", "done\n", "eRMSD = 2.32\n", "\n", "Processing R1116 (length 157)...\n", "Converted /app/Kaggle/StanfordRNA/MSA/R1116.MSA.fasta to /app/Kaggle/StanfordRNA/output/R1116.a3m using Python\n", "predict.py\n", "predict.py output: predict SS by SPOT-RNA\n", "predict geometries\n", "done!\n", "saving......\n", "\n", "temp_dir /app/Kaggle/StanfordRNA/output/temp_R1116\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[21], line 131\u001b[0m\n\u001b[1;32m    129\u001b[0m fold_cmd \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpython\u001b[39m\u001b[38;5;124m\"\u001b[39m, FOLD_PY, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-npz\u001b[39m\u001b[38;5;124m\"\u001b[39m, npz_file, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-fa\u001b[39m\u001b[38;5;124m\"\u001b[39m, fasta_file, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-out\u001b[39m\u001b[38;5;124m\"\u001b[39m, pdb_out, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-nm\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m5\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-tmp\u001b[39m\u001b[38;5;124m\"\u001b[39m, temp_dir]\n\u001b[1;32m    130\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 131\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43msubprocess\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfold_cmd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcheck\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcapture_output\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    132\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfold.py output: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresult\u001b[38;5;241m.\u001b[39mstdout\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    133\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m subprocess\u001b[38;5;241m.\u001b[39mCalledProcessError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/miniconda3/envs/copick/lib/python3.12/subprocess.py:550\u001b[0m, in \u001b[0;36mrun\u001b[0;34m(input, capture_output, timeout, check, *popenargs, **kwargs)\u001b[0m\n\u001b[1;32m    548\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m <PERSON>n(\u001b[38;5;241m*\u001b[39mpopenargs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs) \u001b[38;5;28;01mas\u001b[39;00m process:\n\u001b[1;32m    549\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 550\u001b[0m         stdout, stderr \u001b[38;5;241m=\u001b[39m \u001b[43mprocess\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcommunicate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    551\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m TimeoutExpired \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    552\u001b[0m         process\u001b[38;5;241m.\u001b[39mkill()\n", "File \u001b[0;32m~/miniconda3/envs/copick/lib/python3.12/subprocess.py:1209\u001b[0m, in \u001b[0;36mPopen.communicate\u001b[0;34m(self, input, timeout)\u001b[0m\n\u001b[1;32m   1206\u001b[0m     endtime \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1208\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1209\u001b[0m     stdout, stderr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_communicate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mendtime\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1210\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[1;32m   1211\u001b[0m     \u001b[38;5;66;03m# https://bugs.python.org/issue25942\u001b[39;00m\n\u001b[1;32m   1212\u001b[0m     \u001b[38;5;66;03m# See the detailed comment in .wait().\u001b[39;00m\n\u001b[1;32m   1213\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/copick/lib/python3.12/subprocess.py:2115\u001b[0m, in \u001b[0;36mPopen._communicate\u001b[0;34m(self, input, endtime, orig_timeout)\u001b[0m\n\u001b[1;32m   2108\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timeout(endtime, orig_timeout,\n\u001b[1;32m   2109\u001b[0m                         stdout, stderr,\n\u001b[1;32m   2110\u001b[0m                         skip_check_and_raise\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m   2111\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(  \u001b[38;5;66;03m# Impossible :)\u001b[39;00m\n\u001b[1;32m   2112\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_check_timeout(..., skip_check_and_raise=True) \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m   2113\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfailed to raise TimeoutExpired.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m-> 2115\u001b[0m ready \u001b[38;5;241m=\u001b[39m \u001b[43mselector\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2116\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timeout(endtime, orig_timeout, stdout, stderr)\n\u001b[1;32m   2118\u001b[0m \u001b[38;5;66;03m# XXX Rewrite these to use non-blocking I/O on the file\u001b[39;00m\n\u001b[1;32m   2119\u001b[0m \u001b[38;5;66;03m# objects; they are no longer using C stdio!\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/copick/lib/python3.12/selectors.py:415\u001b[0m, in \u001b[0;36m_PollLikeSelector.select\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    413\u001b[0m ready \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    414\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 415\u001b[0m     fd_event_list \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_selector\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpoll\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n\u001b[1;32m    417\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m ready\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# %% [markdown]\n", "# # trRosettaRNA for RNA 3D Structure Prediction Challenge\n", "# This notebook runs the trRosettaRNA pipeline on the competition's test dataset,\n", "# converting FASTA MSAs to A3M, predicting 3D structures, and generating a submission CSV.\n", "from tqdm import tqdm\n", "# %% [code]\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import subprocess\n", "import tempfile\n", "import torch\n", "from Bio import SeqIO\n", "from Bio.Seq import Seq\n", "from Bio.SeqRecord import SeqRecord\n", "import re\n", "from pathlib import Path\n", "\n", "# Paths to trRosettaRNA scripts and data\n", "TRROSETTA_DIR = \"/app/Kaggle/StanfordRNA/trRosettaRNA_v1.1/\"\n", "PREDICT_PY = os.path.join(TRROSETTA_DIR, \"predict.py\")\n", "FOLD_PY = os.path.join(TRROSETTA_DIR, \"fold.py\")\n", "MODEL_DIR = os.path.join(TRROSETTA_DIR, \"params/model_1\")\n", "DATA_DIR = \"/app/Kaggle/StanfordRNA/\"\n", "MSA_DIR = os.path.join(DATA_DIR, \"MSA\")\n", "OUTPUT_DIR = \"/app/Kaggle/StanfordRNA/output/\"\n", "Path(OUTPUT_DIR).mkdir(exist_ok=True)\n", "\n", "# Function to convert FASTA MSA to A3M\n", "def fasta_to_a3m(fasta_file, a3m_file):\n", "    \"\"\"\n", "    Convert FASTA MSA to A3M format using HH-suite's reformat.pl or Python parsing.\n", "    \"\"\"\n", "    try:\n", "        # Option 1: Use HH-suite's reformat.pl (recommended)\n", "        subprocess.run(\n", "            [\"reformat.pl\", \"fas\", \"a3m\", fasta_file, a3m_file],\n", "            check=True, capture_output=True\n", "        )\n", "    except FileNotFoundError:\n", "        # Option 2: Python-based conversion (simplified)\n", "        records = list(SeqIO.parse(fasta_file, \"fasta\"))\n", "        with open(a3m_file, \"w\") as f:\n", "            for rec in records:\n", "                # Remove gaps (except '-') and convert to uppercase\n", "                seq = re.sub(r\"[^A-Z-]\", \"-\", str(rec.seq).upper())\n", "                f.write(f\">{rec.id}\\n{seq}\\n\")\n", "        print(f\"Converted {fasta_file} to {a3m_file} using Python\")\n", "\n", "# Function to create FASTA file from sequence\n", "def create_fasta(target_id, sequence, fasta_file):\n", "    \"\"\"\n", "    Create a FASTA file for a single RNA sequence.\n", "    \"\"\"\n", "    record = SeqRecord(Seq(sequence), id=target_id, description=\"\")\n", "    with open(fasta_file, \"w\") as f:\n", "        SeqIO.write(record, f, \"fasta\")\n", "\n", "# Function to extract C1' coordinates from PDB\n", "def extract_c1p_coordinates(pdb_file, sequence):\n", "    \"\"\"\n", "    Extract C1' atom coordinates from a PDB file for each residue.\n", "    Returns a list of [x, y, z] for C1' atoms, ordered by resid.\n", "    \"\"\"\n", "    coords = []\n", "    L = len(sequence)\n", "    with open(pdb_file, \"r\") as f:\n", "        for line in f:\n", "            if line.startswith(\"ATOM\"):\n", "                atom_name = line[12:16].strip()\n", "                resname = line[17:20].strip()\n", "                resid = int(line[22:26].strip())\n", "                if atom_name == \"C1'\" and resname in [\"A\", \"C\", \"G\", \"U\"] and resid <= L:\n", "                    x = float(line[30:38].strip())\n", "                    y = float(line[38:46].strip())\n", "                    z = float(line[46:54].strip())\n", "                    coords.append([resid, resname, x, y, z])\n", "    # Sort by resid to ensure correct order\n", "    coords.sort(key=lambda x: x[0])\n", "    return [[x[2], x[3], x[4]] for x in coords]  # Return [x, y, z] list\n", "\n", "# Load test sequences\n", "test_df = pd.read_csv(os.path.join(DATA_DIR, \"test_sequences.csv\"))\n", "\n", "# Submission dataframe\n", "submission_rows = []\n", "import glob\n", "\n", "# Process each test sequence\n", "for idx, row in test_df.iterrows():\n", "    target_id = row[\"target_id\"]\n", "    sequence = row[\"sequence\"]\n", "    L = len(sequence)\n", "    \n", "    print(f\"Processing {target_id} (length {L})...\")\n", "    \n", "    # Step 1: Convert MSA\n", "    fasta_msa = os.path.join(MSA_DIR, f\"{target_id}.MSA.fasta\")\n", "    a3m_msa = os.path.join(OUTPUT_DIR, f\"{target_id}.a3m\")\n", "    if not os.path.exists(fasta_msa):\n", "        print(f\"MSA file {fasta_msa} not found, skipping {target_id}\")\n", "        continue\n", "    try:\n", "        fasta_to_a3m(fasta_msa, a3m_msa)\n", "    except Exception as e:\n", "        print(f\"MSA conversion failed for {target_id}: {e}\")\n", "        continue\n", "    \n", "    # Step 2: Create FASTA\n", "    fasta_file = os.path.join(OUTPUT_DIR, f\"{target_id}.fasta\")\n", "    create_fasta(target_id, sequence, fasta_file)\n", "    \n", "    # Step 3: Run predict.py\n", "    npz_file = os.path.join(OUTPUT_DIR, f\"{target_id}.npz\")\n", "    print('predict.py')\n", "    predict_cmd = [\"python\", PREDICT_PY, \"-i\", a3m_msa, \"-o\", npz_file, \"-mdir\", MODEL_DIR, \"-cpu\", \"6\"]\n", "    try:\n", "        result = subprocess.run(predict_cmd, check=True, capture_output=True, text=True)\n", "        print(f\"predict.py output: {result.stdout}\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"predict.py failed for {target_id}: {e.stderr}\")\n", "        continue\n", "    \n", "    # Step 4: Run fold.py\n", "    pdb_out = os.path.join(OUTPUT_DIR, f\"{target_id}.pdb\")\n", "    temp_dir = os.path.join(OUTPUT_DIR, f\"temp_{target_id}\")\n", "    os.makedirs(temp_dir, exist_ok=True)\n", "    print('temp_dir',temp_dir)\n", "    fold_cmd = [\"python\", FOLD_PY, \"-npz\", npz_file, \"-fa\", fasta_file, \"-out\", pdb_out, \"-nm\", \"5\", \"-tmp\", temp_dir]\n", "    try:\n", "        result = subprocess.run(fold_cmd, check=True, capture_output=True, text=True)\n", "        print(f\"fold.py output: {result.stdout}\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"fold.py failed for {target_id}: {e.stderr}\")\n", "        continue\n", "    \n", "    # # Step 5: Find temporary subdirectory\n", "    # temp_subdirs = glob.glob(f\"{temp_dir}/*/\")\n", "    # if temp_subdirs:\n", "    #     temp_dir = temp_subdirs[0]\n", "    #     print(f\"Using temporary subdirectory: {temp_dir}\")\n", "    # else:\n", "    #     print(f\"No temporary subdirectory found in {temp_dir}\")\n", "    #     continue\n", "    # Step 5: Check for PDB files\n", "    pdb_files = glob.glob(f\"{temp_dir}/model_*.pdb\")\n", "    if len(pdb_files) < 5:\n", "        print(f\"Found only {len(pdb_files)} PDB files in {temp_dir}, expected 5\")\n", "        # Fallback: Search subdirectories\n", "        pdb_files = glob.glob(f\"{temp_dir}/**/model_*.pdb\", recursive=True)\n", "        if len(pdb_files) < 5 and os.path.exists(pdb_out):\n", "            print(f\"Falling back to primary PDB {pdb_out} for {target_id}\")\n", "            coords = extract_c1p_coordinates(pdb_out, sequence)\n", "            if len(coords) == L:\n", "                all_coords = [coords] * 5\n", "            else:\n", "                print(f\"Warning: Incomplete coordinates in {pdb_out} for {target_id} ({len(coords)}/{L})\")\n", "                continue\n", "        elif len(pdb_files) < 5:\n", "            print(f\"Still insufficient PDB files ({len(pdb_files)}) for {target_id}\")\n", "            continue\n", "            \n", "\n", "    # Step 6: Extract C1' coordinates\n", "    all_coords = []\n", "    for i in range(1, 6):\n", "        pdb_file = next((f for f in pdb_files if f.endswith(f\"model_{i}.pdb\")), None)\n", "        if pdb_file and os.path.exists(pdb_file):\n", "            coords = extract_c1p_coordinates(pdb_file, sequence)\n", "            if len(coords) == L:\n", "                all_coords.append(coords)\n", "            else:\n", "                print(f\"Warning: Incomplete coordinates in {pdb_file} for {target_id} ({len(coords)}/{L})\")\n", "        else:\n", "            print(f\"Warning: {pdb_file} not found for {target_id}\")\n", "    \n", "    # Step 7: Format submission rows\n", "    if len(all_coords) == 5:\n", "        for resid in range(1, L + 1):\n", "            ID = f\"{target_id}_{resid}\"\n", "            resname = sequence[resid - 1]\n", "            row_dict = {\"ID\": ID, \"resname\": resname, \"resid\": resid}\n", "            for i, coords in enumerate(all_coords, 1):\n", "                x, y, z = coords[resid - 1]\n", "                row_dict[f\"x_{i}\"] = x\n", "                row_dict[f\"y_{i}\"] = y\n", "                row_dict[f\"z_{i}\"] = z\n", "            submission_rows.append(row_dict)\n", "    else:\n", "        print(f\"Warning: Insufficient structures for {target_id} ({len(all_coords)}/5), skipping submission\")\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "87ac5d42-8fd6-4975-bfb0-d2d9e2a749b8", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['/app/Kaggle/StanfordRNA/output/temp_R1128/h1_fs6ym/']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["test_df\n", "temp_dir\n", "glob.glob(f\"{temp_dir}/*/\")"]}, {"cell_type": "code", "execution_count": null, "id": "f586e885-59f8-49e2-95af-1bdb522e47d4", "metadata": {}, "outputs": [], "source": ["\n", "# Step 7: Save submission CSV\n", "submission_df = pd.DataFrame(submission_rows)\n", "submission_cols = [\"ID\", \"resname\", \"resid\"] + \\\n", "                 [f\"{c}_{i}\" for i in range(1, 6) for c in [\"x\", \"y\", \"z\"]]\n", "submission_df = submission_df[submission_cols]  # Reorder columns\n", "submission_df.to_csv(\"submission.csv\", index=False)\n", "print(\"Submission saved to submission.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "72b2a9d3-54c8-440e-917a-e070a729ed96", "metadata": {}, "outputs": [], "source": ["\n", "# # Save submission CSV \n", "# # Process each test sequence\n", "# for idx, row in tqdm(test_df.iterrows()):\n", "#     target_id = row[\"target_id\"]\n", "#     sequence = row[\"sequence\"]\n", "#     L = len(sequence)\n", "    \n", "#     print(f\"Processing {target_id} (length {L})...\")\n", "    \n", "#     # Step 1: Convert MSA from FASTA to A3M\n", "#     fasta_msa = os.path.join(MSA_DIR, f\"{target_id}.MSA.fasta\")\n", "#     a3m_msa = os.path.join(OUTPUT_DIR, f\"{target_id}.a3m\")\n", "#     if not os.path.exists(fasta_msa):\n", "#         print(f\"MSA file {fasta_msa} not found, skipping {target_id}\")\n", "#         continue\n", "#     fasta_to_a3m(fasta_msa, a3m_msa)\n", "    \n", "#     # Step 2: Create FASTA file for folding\n", "#     fasta_file = os.path.join(OUTPUT_DIR, f\"{target_id}.fasta\")\n", "#     create_fasta(target_id, sequence, fasta_file)\n", "    \n", "#     # Step 3: Run predict.py to generate NPZ\n", "#     npz_file = os.path.join(OUTPUT_DIR, f\"{target_id}.npz\")\n", "#     predict_cmd = [\n", "#         \"python\", PREDICT_PY,\n", "#         \"-i\", a3m_msa,\n", "#         \"-o\", npz_file,\n", "#         \"-mdir\", MODEL_DIR,\n", "#         \"-gpu\", \"0\"  # Adjust GPU ID as needed\n", "#     ]\n", "#     try:\n", "#         subprocess.run(predict_cmd, check=True)\n", "#     except subprocess.CalledProcessError as e:\n", "#         print(f\"Error running predict.py for {target_id}: {e}\")\n", "#         continue\n", "    \n", "#     # Step 4: Run fold.py to generate 5 PDBs\n", "#     pdb_out = os.path.join(OUTPUT_DIR, f\"{target_id}.pdb\")\n", "#     temp_dir = tempfile.mkdtemp()  # Temporary directory for decoys\n", "#     fold_cmd = [\n", "#         \"python\", FOLD_PY,\n", "#         \"-npz\", npz_file,\n", "#         \"-fa\", fasta_file,\n", "#         \"-out\", pdb_out,\n", "#         \"-nm\", \"5\",  # Generate 5 decoys\n", "#         \"-tmp\", temp_dir\n", "#     ]\n", "#     try:\n", "#         subprocess.run(fold_cmd, check=True)\n", "#     except subprocess.CalledProcessError as e:\n", "#         print(f\"Error running fold.py for {target_id}: {e}\")\n", "#         continue\n", "    \n", "#     # Step 5: Extract C1' coordinates from 5 PDBs\n", "#     all_coords = []\n", "#     for i in range(1, 6):\n", "#         pdb_file = os.path.join(temp_dir, f\"model_{i}.pdb\")\n", "#         if os.path.exists(pdb_file):\n", "#             coords = extract_c1p_coordinates(pdb_file, sequence)\n", "#             if len(coords) == L:\n", "#                 all_coords.append(coords)\n", "#             else:\n", "#                 print(f\"Warning: Incomplete coordinates in {pdb_file} for {target_id}\")\n", "#         else:\n", "#             print(f\"Warning: {pdb_file} not found for {target_id}\")\n", "    \n", "#     # Step 6: Format submission rows\n", "#     if len(all_coords) == 5:  # Ensure all 5 structures are available\n", "#         for resid in range(1, L + 1):\n", "#             ID = f\"{target_id}_{resid}\"\n", "#             resname = sequence[resid - 1]\n", "#             # Collect coordinates for all 5 structures\n", "#             coord_cols = []\n", "#             for i, coords in enumerate(all_coords, 1):\n", "#                 x, y, z = coords[resid - 1]  # 0-based index for coords\n", "#                 coord_cols.extend([f\"x_{i}\", f\"y_{i}\", f\"z_{i}\"])\n", "#                 coord_cols.extend([x, y, z])\n", "#             # Create row dictionary\n", "#             row_dict = {\"ID\": ID, \"resname\": resname, \"resid\": resid}\n", "#             for col, val in zip(coord_cols[::4], coord_cols[1::4], coord_cols[2::4], coord_cols[3::4]):\n", "#                 row_dict[col] = val\n", "#             submission_rows.append(row_dict)\n", "#     else:\n", "#         print(f\"Warning: Insufficient structures for {target_id}, skipping submission\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a6847f04-8b74-4c48-8121-91163692cc96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "634c68e1-2f5d-4d4e-96a6-efd026098627", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>target_id</th>\n", "      <th>sequence</th>\n", "      <th>temporal_cutoff</th>\n", "      <th>description</th>\n", "      <th>all_sequences</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1SCL_A</td>\n", "      <td>GGGUGCUCAGUACGAGAGGAACCGCACCC</td>\n", "      <td>1995-01-26</td>\n", "      <td>THE SARCIN-RICIN LOOP, A MODULAR RNA</td>\n", "      <td>&gt;1SCL_1|Chain A|RNA SARCIN-RICIN LOOP|Rattus n...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1RNK_A</td>\n", "      <td>GGCGCAGUGGGCUAGCGCCACUCAAAAGGCCCAU</td>\n", "      <td>1995-02-27</td>\n", "      <td>THE STRUCTURE OF AN RNA PSEUDOKNOT THAT CAUSES...</td>\n", "      <td>&gt;1RNK_1|Chain A|RNA PSEUDOKNOT|null\\nGGCGCAGUG...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1RHT_A</td>\n", "      <td>GGGACUGACGAUCACGCAGUCUAU</td>\n", "      <td>1995-06-03</td>\n", "      <td>24-MER RNA HAIRPIN COAT PROTEIN BINDING SITE F...</td>\n", "      <td>&gt;1RHT_1|Chain A|RNA (5'-R(P*GP*GP*GP*AP*CP*UP*...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1HLX_A</td>\n", "      <td>GGGAUAACUUCGGUUGUCCC</td>\n", "      <td>1995-09-15</td>\n", "      <td>P1 HELIX NUCLEIC ACIDS (DNA/RNA) RIBONUCLEIC ACID</td>\n", "      <td>&gt;1HLX_1|Chain A|RNA (5'-R(*GP*GP*GP*AP*UP*AP*A...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1HMH_E</td>\n", "      <td>GGCGACCCUGAUGAGGCCGAAAGGCCGAAACCGU</td>\n", "      <td>1995-12-07</td>\n", "      <td>THREE-DIMENSIONAL STRUCTURE OF A HAMMERHEAD RI...</td>\n", "      <td>&gt;1HMH_1|Chains A, C, E|HAMMERHEAD RIBOZYME-RNA...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>839</th>\n", "      <td>8T3E_EC</td>\n", "      <td>AAACUCCAUGUAUUGGUUACCCAUCUGCAUCGAAAACUCUCCGAAC...</td>\n", "      <td>2024-12-11</td>\n", "      <td>Hypomethylated yeast 80S bound with Taura synd...</td>\n", "      <td>&gt;8T3E_1|Chain A[auth BA]|40S ribosomal protein...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>840</th>\n", "      <td>8T3F_EC</td>\n", "      <td>AAACUCCAUGUAUUGGUUACCCAUCUGCAUCGAAAACUCUCCGAAC...</td>\n", "      <td>2024-12-11</td>\n", "      <td>Hypomethylated yeast 80S bound with Taura synd...</td>\n", "      <td>&gt;8T3F_1|Chain A[auth BA]|40S ribosomal protein...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>841</th>\n", "      <td>8XCC_B</td>\n", "      <td>GUGCUGCUGUCUCCCAGACGGGAGGCAGAACUGCACCUUCCAUCAG...</td>\n", "      <td>2024-12-11</td>\n", "      <td>Cryo-EM structure of Cas12j19 (E100K), crRNA a...</td>\n", "      <td>&gt;8XCC_1|Chain A|Cas12j19(E100K)|unclassified s...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>842</th>\n", "      <td>8Z1G_T</td>\n", "      <td>GGUAAAAUGGCUGAGUGAAGCAUUGGACUGUAAAUCUAAAGACAGG...</td>\n", "      <td>2024-12-18</td>\n", "      <td>Cryo-EM structure of human ELAC2-pre-tRNA</td>\n", "      <td>&gt;8Z1G_1|Chain A[auth T]|Homo sapiens mitochond...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>843</th>\n", "      <td>8Z1F_T</td>\n", "      <td>GGUAAAAUGGCUGAGUGAAGCAUUGGACUGUAAAUCUAAAGACAGG...</td>\n", "      <td>2024-12-18</td>\n", "      <td>Cryo-EM structure of human ELAC2-tRNA</td>\n", "      <td>&gt;8Z1F_1|Chain A[auth T]|Homo sapiens mitochond...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>844 rows × 5 columns</p>\n", "</div>"], "text/plain": ["    target_id                                           sequence  \\\n", "0      1SCL_A                      GGGUGCUCAGUACGAGAGGAACCGCACCC   \n", "1      1RNK_A                 GGCGCAGUGGGCUAGCGCCACUCAAAAGGCCCAU   \n", "2      1RHT_A                           GGGACUGACGAUCACGCAGUCUAU   \n", "3      1HLX_A                               GGGAUAACUUCGGUUGUCCC   \n", "4      1HMH_E                 GGCGACCCUGAUGAGGCCGAAAGGCCGAAACCGU   \n", "..        ...                                                ...   \n", "839   8T3E_EC  AAACUCCAUGUAUUGGUUACCCAUCUGCAUCGAAAACUCUCCGAAC...   \n", "840   8T3F_EC  AAACUCCAUGUAUUGGUUACCCAUCUGCAUCGAAAACUCUCCGAAC...   \n", "841    8XCC_B  GUGCUGCUGUCUCCCAGACGGGAGGCAGAACUGCACCUUCCAUCAG...   \n", "842    8Z1G_T  GGUAAAAUGGCUGAGUGAAGCAUUGGACUGUAAAUCUAAAGACAGG...   \n", "843    8Z1F_T  GGUAAAAUGGCUGAGUGAAGCAUUGGACUGUAAAUCUAAAGACAGG...   \n", "\n", "    temporal_cutoff                                        description  \\\n", "0        1995-01-26               THE SARCIN-R<PERSON><PERSON> LOOP, A MODULAR RNA   \n", "1        1995-02-27  THE STRUCTURE OF AN RNA PSEUDOKNOT THAT CAUSES...   \n", "2        1995-06-03  24-<PERSON><PERSON> RNA HAIRPIN COAT PROTEIN BINDING SITE F...   \n", "3        1995-09-15  P1 HELIX NUCLEIC ACIDS (DNA/RNA) RIBONUCLEIC ACID   \n", "4        1995-12-07  THREE-DIMENSIONAL STRUCTURE OF A HAMMERHEAD RI...   \n", "..              ...                                                ...   \n", "839      2024-12-11  Hypomethylated yeast 80S bound with Taura synd...   \n", "840      2024-12-11  Hypomethylated yeast 80S bound with Taura synd...   \n", "841      2024-12-11  Cryo-EM structure of Cas12j19 (E100K), crRNA a...   \n", "842      2024-12-18          Cryo-EM structure of human ELAC2-pre-tRNA   \n", "843      2024-12-18              Cryo-EM structure of human ELAC2-tRNA   \n", "\n", "                                         all_sequences  \n", "0    >1SCL_1|Chain A|RNA SARCIN-RICIN LOOP|Rattus n...  \n", "1    >1RNK_1|Chain A|RNA PSEUDOKNOT|null\\nGGCGCAGUG...  \n", "2    >1RHT_1|Chain A|RNA (5'-R(P*GP*GP*GP*AP*CP*UP*...  \n", "3    >1HLX_1|Chain A|RNA (5'-R(*GP*GP*GP*AP*UP*AP*A...  \n", "4    >1HMH_1|Chains A, C, E|HAMMERHEAD RIBOZYME-RNA...  \n", "..                                                 ...  \n", "839  >8T3E_1|Chain A[auth BA]|40S ribosomal protein...  \n", "840  >8T3F_1|Chain A[auth BA]|40S ribosomal protein...  \n", "841  >8XCC_1|Chain A|Cas12j19(E100K)|unclassified s...  \n", "842  >8Z1G_1|Chain A[auth T]|Homo sapiens mitochond...  \n", "843  >8Z1F_1|Chain A[auth T]|Homo sapiens mitochond...  \n", "\n", "[844 rows x 5 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>resname</th>\n", "      <th>resid</th>\n", "      <th>x_1</th>\n", "      <th>y_1</th>\n", "      <th>z_1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1SCL_A_1</td>\n", "      <td>G</td>\n", "      <td>1</td>\n", "      <td>13.760</td>\n", "      <td>-25.974001</td>\n", "      <td>0.102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1SCL_A_2</td>\n", "      <td>G</td>\n", "      <td>2</td>\n", "      <td>9.310</td>\n", "      <td>-29.638000</td>\n", "      <td>2.669</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1SCL_A_3</td>\n", "      <td>G</td>\n", "      <td>3</td>\n", "      <td>5.529</td>\n", "      <td>-27.813000</td>\n", "      <td>5.878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1SCL_A_4</td>\n", "      <td>U</td>\n", "      <td>4</td>\n", "      <td>2.678</td>\n", "      <td>-24.900999</td>\n", "      <td>9.793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1SCL_A_5</td>\n", "      <td>G</td>\n", "      <td>5</td>\n", "      <td>1.827</td>\n", "      <td>-20.136000</td>\n", "      <td>11.793</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137090</th>\n", "      <td>8Z1F_T_82</td>\n", "      <td>U</td>\n", "      <td>82</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137091</th>\n", "      <td>8Z1F_T_83</td>\n", "      <td>C</td>\n", "      <td>83</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137092</th>\n", "      <td>8Z1F_T_84</td>\n", "      <td>A</td>\n", "      <td>84</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137093</th>\n", "      <td>8Z1F_T_85</td>\n", "      <td>U</td>\n", "      <td>85</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137094</th>\n", "      <td>8Z1F_T_86</td>\n", "      <td>A</td>\n", "      <td>86</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>137095 rows × 6 columns</p>\n", "</div>"], "text/plain": ["               ID resname  resid     x_1        y_1     z_1\n", "0        1SCL_A_1       G      1  13.760 -25.974001   0.102\n", "1        1SCL_A_2       G      2   9.310 -29.638000   2.669\n", "2        1SCL_A_3       G      3   5.529 -27.813000   5.878\n", "3        1SCL_A_4       U      4   2.678 -24.900999   9.793\n", "4        1SCL_A_5       G      5   1.827 -20.136000  11.793\n", "...           ...     ...    ...     ...        ...     ...\n", "137090  8Z1F_T_82       U     82     NaN        NaN     NaN\n", "137091  8Z1F_T_83       C     83     NaN        NaN     NaN\n", "137092  8Z1F_T_84       A     84     NaN        NaN     NaN\n", "137093  8Z1F_T_85       U     85     NaN        NaN     NaN\n", "137094  8Z1F_T_86       A     86     NaN        NaN     NaN\n", "\n", "[137095 rows x 6 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(train_sequences)\n", "display(train_labels)\n", "# display(validation_sequences)\n", "# display(validation_labels)\n"]}, {"cell_type": "code", "execution_count": null, "id": "e2e19b7d-731c-4c21-95e4-f65df9c9a9bd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "3b0f3f80-3cd0-481c-b4da-97e1fbe5c071", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "SHAPE data length must match sequence length", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 416\u001b[0m\n\u001b[1;32m    414\u001b[0m \u001b[38;5;66;03m# Mock SHAPE data (higher values = more flexible/unpaired)\u001b[39;00m\n\u001b[1;32m    415\u001b[0m shape_data \u001b[38;5;241m=\u001b[39m [\u001b[38;5;241m0.8\u001b[39m, \u001b[38;5;241m0.9\u001b[39m, \u001b[38;5;241m0.7\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.3\u001b[39m, \u001b[38;5;241m0.7\u001b[39m, \u001b[38;5;241m0.8\u001b[39m, \u001b[38;5;241m0.9\u001b[39m, \u001b[38;5;241m0.7\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;241m0.2\u001b[39m, \u001b[38;5;241m0.1\u001b[39m]\n\u001b[0;32m--> 416\u001b[0m \u001b[43mpredictor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincorporate_shape_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mshape_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    418\u001b[0m \u001b[38;5;66;03m# Mock evolutionary data\u001b[39;00m\n\u001b[1;32m    419\u001b[0m msa \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m    420\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGGGAAACCCUUUGGGUUUCCC\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# Reference sequence\u001b[39;00m\n\u001b[1;32m    421\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGGGAAACCCUUUGGGCUUCCC\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# Variant 1\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    424\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGGCAAACCCUUUGGGUUUGCC\u001b[39m\u001b[38;5;124m\"\u001b[39m   \u001b[38;5;66;03m# Variant 4 (compensatory mutation)\u001b[39;00m\n\u001b[1;32m    425\u001b[0m ]\n", "Cell \u001b[0;32mIn[1], line 85\u001b[0m, in \u001b[0;36mRNACausalFoldingPredictor.incorporate_shape_data\u001b[0;34m(self, shape_data)\u001b[0m\n\u001b[1;32m     83\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Incorporate SHAPE reactivity data as evidence of structural constraints.\"\"\"\u001b[39;00m\n\u001b[1;32m     84\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(shape_data) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlength:\n\u001b[0;32m---> 85\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSHAPE data length must match sequence length\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     87\u001b[0m \u001b[38;5;66;03m# Update node attributes with SHAPE reactivity\u001b[39;00m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlength):\n", "\u001b[0;31mValueError\u001b[0m: SHAPE data length must match sequence length"]}], "source": ["import numpy as np\n", "import networkx as nx\n", "from scipy.stats import entropy\n", "import pandas as pd\n", "from sklearn.ensemble import RandomForestClassifier\n", "import matplotlib.pyplot as plt\n", "from itertools import combinations\n", "\n", "class RNACausalFoldingPredictor:\n", "    \"\"\"\n", "    Predicts RNA secondary structure using causal inference on directed acyclic graphs.\n", "    Integrates thermodynamic, evolutionary, and experimental data within a causal framework.\n", "    \"\"\"\n", "    \n", "    def __init__(self, sequence):\n", "        \"\"\"Initialize with RNA sequence.\"\"\"\n", "        self.sequence = sequence.upper().replace('T', 'U')\n", "        self.length = len(self.sequence)\n", "        self.causal_graph = nx.DiGraph()\n", "        self.base_pairs = []\n", "        self.motifs = []\n", "        self.interventional_data = {}\n", "        \n", "    def construct_base_centric_graph(self):\n", "        \"\"\"Construct base-centric causal graph where nodes are nucleotide positions.\"\"\"\n", "        # Add nodes (nucleotide positions)\n", "        for i in range(self.length):\n", "            self.causal_graph.add_node(i, nucleotide=self.sequence[i], state=\"unpaired\")\n", "        \n", "        # Add edges based on Watson-Crick complementarity (potential base pairs)\n", "        wc_pairs = {'A': 'U', 'U': 'A', 'G': 'C', 'C': 'G', 'G': 'U', 'U': 'G'}\n", "        \n", "        for i, j in combinations(range(self.length), 2):\n", "            if j - i > 3:  # Minimum loop size constraint\n", "                if self.sequence[i] in wc_pairs and wc_pairs[self.sequence[i]] == self.sequence[j]:\n", "                    # Create potential causal relationships between complementary bases\n", "                    self.causal_graph.add_edge(i, j, type=\"potential_pair\", strength=0.0)\n", "                    self.causal_graph.add_edge(j, i, type=\"potential_pair\", strength=0.0)\n", "    \n", "    def construct_motif_centric_graph(self):\n", "        \"\"\"Construct motif-centric causal graph where nodes are structural motifs.\"\"\"\n", "        # Identify potential stems (consecutive base pairs)\n", "        potential_stems = self._identify_potential_stems()\n", "        \n", "        # Add motif nodes\n", "        for i, stem in enumerate(potential_stems):\n", "            self.causal_graph.add_node(f\"stem_{i}\", type=\"stem\", base_pairs=stem, state=\"inactive\")\n", "            \n", "        # Add edges between competing stems (those sharing nucleotides)\n", "        for i, stem_i in enumerate(potential_stems):\n", "            stem_i_positions = set([pos for pair in stem_i for pos in pair])\n", "            for j, stem_j in enumerate(potential_stems):\n", "                if i != j:\n", "                    stem_j_positions = set([pos for pair in stem_j for pos in pair])\n", "                    if stem_i_positions.intersection(stem_j_positions):\n", "                        # These stems compete (share nucleotides)\n", "                        self.causal_graph.add_edge(f\"stem_{i}\", f\"stem_{j}\", type=\"competes_with\", strength=-0.8)\n", "                        self.causal_graph.add_edge(f\"stem_{j}\", f\"stem_{i}\", type=\"competes_with\", strength=-0.8)\n", "    \n", "    def _identify_potential_stems(self, min_length=2):\n", "        \"\"\"Identify potential stem regions based on complementary sequences.\"\"\"\n", "        potential_stems = []\n", "        wc_pairs = {'A': 'U', 'U': 'A', 'G': 'C', 'C': 'G', 'G': 'U', 'U': 'G'}\n", "        \n", "        for i in range(self.length - min_length):\n", "            for j in range(i + 3, self.length - min_length + 1):\n", "                # Check if we can form a stem of at least min_length\n", "                stem_pairs = []\n", "                for k in range(min_length):\n", "                    if (i+k < self.length and j-k > i+k and \n", "                        self.sequence[i+k] in wc_pairs and \n", "                        wc_pairs[self.sequence[i+k]] == self.sequence[j-k]):\n", "                        stem_pairs.append((i+k, j-k))\n", "                    else:\n", "                        break\n", "                \n", "                if len(stem_pairs) >= min_length:\n", "                    potential_stems.append(stem_pairs)\n", "        \n", "        return potential_stems\n", "    \n", "    def incorporate_shape_data(self, shape_data):\n", "        \"\"\"Incorporate SHAPE reactivity data as evidence of structural constraints.\"\"\"\n", "        if len(shape_data) != self.length:\n", "            raise ValueError(\"SHAPE data length must match sequence length\")\n", "        \n", "        # Update node attributes with SHAPE reactivity\n", "        for i in range(self.length):\n", "            self.causal_graph.nodes[i]['shape_reactivity'] = shape_data[i]\n", "            \n", "            # High SHAPE reactivity suggests unpaired state\n", "            pairing_probability = 1.0 - min(1.0, shape_data[i] / 0.7)\n", "            for j in range(self.length):\n", "                if self.causal_graph.has_edge(i, j) and self.causal_graph[i][j]['type'] == \"potential_pair\":\n", "                    self.causal_graph[i][j]['strength'] *= pairing_probability\n", "    \n", "    def incorporate_evolutionary_data(self, msa):\n", "        \"\"\"Incorporate multiple sequence alignment data to identify covarying positions.\"\"\"\n", "        if not msa or len(msa[0]) != self.length:\n", "            raise ValueError(\"MSA sequences must match reference sequence length\")\n", "        \n", "        # Calculate mutual information between all position pairs\n", "        mutual_info = np.zeros((self.length, self.length))\n", "        \n", "        for i in range(self.length):\n", "            for j in range(i+4, self.length):  # Minimum loop size\n", "                i_nucleotides = [seq[i] for seq in msa]\n", "                j_nucleotides = [seq[j] for seq in msa]\n", "                \n", "                # Skip if constant positions\n", "                if len(set(i_nucleotides)) == 1 or len(set(j_nucleotides)) == 1:\n", "                    continue\n", "                \n", "                # Calculate mutual information\n", "                mi = self._calculate_mutual_information(i_nucleotides, j_nucleotides)\n", "                mutual_info[i, j] = mi\n", "                mutual_info[j, i] = mi\n", "                \n", "                # High MI suggests causal coupling (likely base pairing)\n", "                if self.causal_graph.has_edge(i, j):\n", "                    # Scale existing edge strength based on MI\n", "                    if mi > 0.5:  # Threshold for significant covariation\n", "                        self.causal_graph[i][j]['strength'] += min(1.0, mi)\n", "                        self.causal_graph[j][i]['strength'] += min(1.0, mi)\n", "                else:\n", "                    # Potential non-canonical interaction\n", "                    if mi > 0.7:  # Higher threshold for new edges\n", "                        self.causal_graph.add_edge(i, j, type=\"covariation\", strength=min(0.8, mi))\n", "                        self.causal_graph.add_edge(j, i, type=\"covariation\", strength=min(0.8, mi))\n", "    \n", "    def _calculate_mutual_information(self, col1, col2):\n", "        \"\"\"Calculate mutual information between two alignment columns.\"\"\"\n", "        # Count joint occurrences\n", "        joint_counts = {}\n", "        for x, y in zip(col1, col2):\n", "            if (x, y) not in joint_counts:\n", "                joint_counts[(x, y)] = 0\n", "            joint_counts[(x, y)] += 1\n", "        \n", "        # Count individual occurrences\n", "        x_counts = {}\n", "        for x in col1:\n", "            if x not in x_counts:\n", "                x_counts[x] = 0\n", "            x_counts[x] += 1\n", "            \n", "        y_counts = {}\n", "        for y in col2:\n", "            if y not in y_counts:\n", "                y_counts[y] = 0\n", "            y_counts[y] += 1\n", "        \n", "        # Calculate probabilities\n", "        n = len(col1)\n", "        joint_probs = {pair: count/n for pair, count in joint_counts.items()}\n", "        px = {x: count/n for x, count in x_counts.items()}\n", "        py = {y: count/n for y, count in y_counts.items()}\n", "        \n", "        # Calculate mutual information\n", "        mi = 0\n", "        for (x, y), p_xy in joint_probs.items():\n", "            mi += p_xy * np.log2(p_xy / (px[x] * py[y]))\n", "        \n", "        return mi\n", "    \n", "    def apply_do_calculus(self, intervention_node, intervention_value):\n", "        \"\"\"Apply do-calculus to simulate intervention on a node.\"\"\"\n", "        # Create copy of the graph for intervention\n", "        intervened_graph = self.causal_graph.copy()\n", "        \n", "        # Remove incoming edges to the intervention node (do-operator)\n", "        if intervention_node in intervened_graph:\n", "            incoming_edges = list(intervened_graph.in_edges(intervention_node))\n", "            for u, v in incoming_edges:\n", "                intervened_graph.remove_edge(u, v)\n", "            \n", "            # Set the value of the intervention node\n", "            intervened_graph.nodes[intervention_node]['state'] = intervention_value\n", "            \n", "            # Store the intervened graph\n", "            self.interventional_data[f\"do({intervention_node}={intervention_value})\"] = intervened_graph\n", "            \n", "            return intervened_graph\n", "        else:\n", "            raise ValueError(f\"Node {intervention_node} not found in the causal graph\")\n", "    \n", "    def simulate_mutations(self, positions):\n", "        \"\"\"Simulate the effect of mutations at specified positions using do-calculus.\"\"\"\n", "        mutation_effects = {}\n", "        \n", "        nucleotides = ['A', 'C', 'G', 'U']\n", "        original_structure = self.predict_structure()\n", "        \n", "        for pos in positions:\n", "            original_nt = self.sequence[pos]\n", "            pos_effects = {}\n", "            \n", "            for new_nt in nucleotides:\n", "                if new_nt != original_nt:\n", "                    # Create mutated sequence\n", "                    mutated_seq = self.sequence[:pos] + new_nt + self.sequence[pos+1:]\n", "                    \n", "                    # Apply do-calculus to simulate mutation\n", "                    mutation_predictor = RNACausalFoldingPredictor(mutated_seq)\n", "                    mutation_predictor.construct_base_centric_graph()\n", "                    \n", "                    # Copy over any experimental data\n", "                    for node, attrs in self.causal_graph.nodes(data=True):\n", "                        if node != pos and node in mutation_predictor.causal_graph:\n", "                            for key, value in attrs.items():\n", "                                if key != 'nucleotide':\n", "                                    mutation_predictor.causal_graph.nodes[node][key] = value\n", "                    \n", "                    # Predict structure with mutation\n", "                    mutated_structure = mutation_predictor.predict_structure()\n", "                    \n", "                    # Calculate structural distance\n", "                    distance = self._calculate_structure_distance(original_structure, mutated_structure)\n", "                    pos_effects[new_nt] = {\n", "                        'structural_distance': distance,\n", "                        'predicted_structure': mutated_structure\n", "                    }\n", "            \n", "            mutation_effects[pos] = pos_effects\n", "        \n", "        return mutation_effects\n", "    \n", "    def _calculate_structure_distance(self, structure1, structure2):\n", "        \"\"\"Calculate Hamming distance between two secondary structures.\"\"\"\n", "        if len(structure1) != len(structure2):\n", "            raise ValueError(\"Structures must have the same length\")\n", "        \n", "        distance = sum(1 for a, b in zip(structure1, structure2) if a != b)\n", "        return distance\n", "    \n", "    def predict_structure(self, method=\"max_causal_flow\"):\n", "        \"\"\"Predict RNA secondary structure using causal inference.\"\"\"\n", "        if method == \"max_causal_flow\":\n", "            return self._predict_by_max_causal_flow()\n", "        elif method == \"mcmc\":\n", "            return self._predict_by_mcmc()\n", "        else:\n", "            raise ValueError(f\"Unknown prediction method: {method}\")\n", "    \n", "    def _predict_by_max_causal_flow(self, iterations=100):\n", "        \"\"\"Predict structure by iteratively maximizing causal flow.\"\"\"\n", "        # Initialize all nodes as unpaired\n", "        current_structure = {i: \".\" for i in range(self.length)}\n", "        \n", "        # Iterate until convergence or max iterations\n", "        for _ in range(iterations):\n", "            modified = False\n", "            \n", "            # Sort edges by strength (highest first)\n", "            edges = [(u, v, d) for u, v, d in self.causal_graph.edges(data=True) \n", "                    if isinstance(u, int) and isinstance(v, int) and u < v]  # Only consider each pair once\n", "            edges.sort(key=lambda x: x[2].get('strength', 0), reverse=True)\n", "            \n", "            for i, j, data in edges:\n", "                if data.get('strength', 0) > 0.5:  # Threshold for considering a pair\n", "                    # Check if both positions are unpaired\n", "                    if current_structure[i] == \".\" and current_structure[j] == \".\":\n", "                        # Form base pair\n", "                        current_structure[i] = \"(\"\n", "                        current_structure[j] = \")\"\n", "                        modified = True\n", "            \n", "            if not modified:\n", "                break\n", "        \n", "        # Convert dictionary to dot-bracket notation\n", "        dot_bracket = \"\".join(current_structure[i] for i in sorted(current_structure.keys()))\n", "        return dot_bracket\n", "    \n", "    def _predict_by_mcmc(self, steps=10000, temp=1.0):\n", "        \"\"\"Predict structure using Markov Chain Monte <PERSON> sampling.\"\"\"\n", "        # Initialize with a random structure\n", "        current_structure = self._generate_random_structure()\n", "        current_energy = self._calculate_causal_energy(current_structure)\n", "        \n", "        best_structure = current_structure\n", "        best_energy = current_energy\n", "        \n", "        # Run MCMC\n", "        for step in range(steps):\n", "            # Generate neighbor by flipping one base pair\n", "            neighbor = self._generate_neighbor(current_structure)\n", "            neighbor_energy = self._calculate_causal_energy(neighbor)\n", "            \n", "            # Metropolis acceptance criterion\n", "            delta_e = neighbor_energy - current_energy\n", "            if delta_e < 0 or np.random.random() < np.exp(-delta_e / temp):\n", "                current_structure = neighbor\n", "                current_energy = neighbor_energy\n", "                \n", "                # Update best structure\n", "                if current_energy < best_energy:\n", "                    best_structure = current_structure\n", "                    best_energy = current_energy\n", "            \n", "            # Annealing schedule\n", "            temp = max(0.01, temp * 0.9999)\n", "        \n", "        return best_structure\n", "    \n", "    def _generate_random_structure(self):\n", "        \"\"\"Generate a random valid RNA structure.\"\"\"\n", "        # Simple implementation that just returns an unpaired structure\n", "        return \".\" * self.length\n", "    \n", "    def _generate_neighbor(self, structure):\n", "        \"\"\"Generate a neighboring structure by adding/removing/changing a base pair.\"\"\"\n", "        # Simple implementation that flips a random position between \".\" and \"(\"/\")\"\n", "        # A real implementation would maintain valid structures\n", "        i = np.random.randint(self.length)\n", "        neighbor = list(structure)\n", "        if neighbor[i] == \".\":\n", "            neighbor[i] = \"(\"\n", "        else:\n", "            neighbor[i] = \".\"\n", "        return \"\".join(neighbor)\n", "    \n", "    def _calculate_causal_energy(self, structure):\n", "        \"\"\"Calculate the 'energy' of a structure based on causal consistency.\"\"\"\n", "        # Higher energy = less favorable\n", "        energy = 0.0\n", "        \n", "        # Convert dot-bracket to pairs\n", "        pairs = []\n", "        stack = []\n", "        for i, c in enumerate(structure):\n", "            if c == \"(\":\n", "                stack.append(i)\n", "            elif c == \")\":\n", "                if stack:\n", "                    pairs.append((stack.pop(), i))\n", "        \n", "        # Check causal consistency\n", "        for i, j in pairs:\n", "            # If there's a strong causal edge, reduce energy\n", "            if self.causal_graph.has_edge(i, j):\n", "                energy -= self.causal_graph[i][j].get('strength', 0) * 10\n", "            else:\n", "                # Penalize pairs without causal support\n", "                energy += 5\n", "            \n", "            # Check for conflicting pairs (pseudoknots, etc)\n", "            for m, n in pairs:\n", "                if i < m < j < n or m < i < n < j:\n", "                    # Crossing pairs (pseudoknots) are penalized\n", "                    energy += 20\n", "        \n", "        return energy\n", "    \n", "    def visualize_causal_graph(self, highlight_pairs=True):\n", "        \"\"\"Visualize the causal graph with optional highlighting of base pairs.\"\"\"\n", "        plt.figure(figsize=(12, 10))\n", "        \n", "        # Create position dictionary (circular layout)\n", "        pos = nx.circular_layout(self.causal_graph)\n", "        \n", "        # Draw the graph\n", "        nx.draw_networkx_nodes(self.causal_graph, pos, node_size=200, \n", "                               node_color='lightblue', alpha=0.8)\n", "        \n", "        # Draw edges with different colors based on type\n", "        edge_colors = []\n", "        edge_widths = []\n", "        \n", "        for u, v, data in self.causal_graph.edges(data=True):\n", "            if data.get('type') == 'potential_pair':\n", "                edge_colors.append('green')\n", "                edge_widths.append(1 + 3 * data.get('strength', 0))\n", "            elif data.get('type') == 'covariation':\n", "                edge_colors.append('red')\n", "                edge_widths.append(1 + 3 * data.get('strength', 0))\n", "            elif data.get('type') == 'competes_with':\n", "                edge_colors.append('blue')\n", "                edge_widths.append(1)\n", "            else:\n", "                edge_colors.append('gray')\n", "                edge_widths.append(0.5)\n", "        \n", "        nx.draw_networkx_edges(self.causal_graph, pos, \n", "                              edge_color=edge_colors,\n", "                              width=edge_widths,\n", "                              alpha=0.6)\n", "        \n", "        # Draw labels\n", "        labels = {}\n", "        for node in self.causal_graph.nodes():\n", "            if isinstance(node, int):\n", "                labels[node] = f\"{node}:{self.sequence[node]}\"\n", "            else:\n", "                labels[node] = node\n", "        \n", "        nx.draw_networkx_labels(self.causal_graph, pos, labels=labels, font_size=8)\n", "        \n", "        plt.title(\"RNA Folding Causal Graph\")\n", "        plt.axis('off')\n", "        return plt\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Example hairpin sequence\n", "    rna_seq = \"GGGAAACCCUUUGGGUUUCCC\"\n", "    \n", "    # Initialize predictor\n", "    predictor = RNACausalFoldingPredictor(rna_seq)\n", "    \n", "    # Construct causal graph\n", "    predictor.construct_base_centric_graph()\n", "    \n", "    # Mock SHAPE data (higher values = more flexible/unpaired)\n", "    shape_data = [0.8, 0.9, 0.7, 0.1, 0.2, 0.1, 0.1, 0.2, 0.1, 0.3, 0.7, 0.8, 0.9, 0.7, 0.1, 0.2, 0.1, 0.1, 0.2, 0.1]\n", "    predictor.incorporate_shape_data(shape_data)\n", "    \n", "    # Mock evolutionary data\n", "    msa = [\n", "        \"GGGAAACCCUUUGGGUUUCCC\",  # Reference sequence\n", "        \"GGGAAACCCUUUGGGCUUCCC\",  # Variant 1\n", "        \"GGGAAACCCUUCGGGUUUCCC\",  # Variant 2\n", "        \"GGGAAACCCUUUGGGUCCCCC\",  # Variant 3\n", "        \"GGCAAACCCUUUGGGUUUGCC\"   # Variant 4 (compensatory mutation)\n", "    ]\n", "    predictor.incorporate_evolutionary_data(msa)\n", "    \n", "    # Predict structure\n", "    structure = predictor.predict_structure()\n", "    print(f\"Sequence: {rna_seq}\")\n", "    print(f\"Predicted structure: {structure}\")\n", "    \n", "    # Visualize causal graph\n", "    plt.figure = predictor.visualize_causal_graph()\n", "    plt.show()\n", "    \n", "    # Simulate mutations\n", "    mutation_effects = predictor.simulate_mutations([0, 5, 10])\n", "    for pos, effects in mutation_effects.items():\n", "        print(f\"Position {pos} ({rna_seq[pos]}) mutations:\")\n", "        for nt, data in effects.items():\n", "            print(f\"  {rna_seq[pos]}→{nt}: Distance={data['structural_distance']}\")\n", "            print(f\"     New structure: {data['predicted_structure']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "2001eb86-f1ea-4e76-b146-fc98b3f599c6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e6b380b8-1e29-4d13-82e5-84da091e3c26", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "94791e83-ec90-4f3d-9e1b-ad1133a84f64", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5c2b7e23-8634-4485-9658-836da6e881e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "633472a6-66b3-42dc-875e-9545f766c91a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "28e296e4-f9fc-4662-b78b-f72c5888ccd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7658f9e0-2ce4-49b1-af03-d135cde37b00", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "id": "9fda717b-62a3-4582-99e4-94f3772db787", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from mpl_toolkits.mplot3d import Axes3D\n", "\n", "# Load data\n", "train_seq = pd.read_csv('train_sequences.csv')\n", "train_labels = pd.read_csv('train_labels.csv')\n", "val_seq = pd.read_csv('validation_sequences.csv')\n", "val_labels = pd.read_csv('validation_labels.csv')"]}, {"cell_type": "code", "execution_count": 10, "id": "1576fd10-4223-4dbb-a603-c677e130e2bd", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1IAAAIjCAYAAAAJLyrXAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAawNJREFUeJzt3XlcVPX+x/H3DMuwg6iAKOC+ayqm0ma5oWll2XqttCzLH5pmq7cy08qulVld01b1luatbllZuZtaqSm5b2lpmAq4AaKyDd/fH8jkCChj4IC8no/HPOCc851zPmc4M8x7vud8x2KMMQIAAAAAlJrV3QUAAAAAQGVDkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAqqAMWPGyGKxXJBtXX311br66qsd099//70sFos+++yzC7L9gQMHqm7duhdkW+crMzNT9913nyIiImSxWDRixAh3lwSUqenTp8tisWjt2rXlup38/Hy1bNlSL7zwQrlu50x/53XmQr4eu9PUqVMVHR2t7Oxsd5cClBuCFFDJFL5BKbz5+PgoMjJS8fHxeuONN3Ts2LEy2c7+/fs1ZswYrV+/vkzWV5Yqcm2l8eKLL2r69OkaMmSIPvzwQ911110ltq1bt67T39vf318dOnTQf/7znyJtC0OrxWJRYmJikeUDBw5UQEBAidvq0KGDLBaLpkyZ4tL+HDx4UMOHD1fTpk3l6+ursLAwdejQQU888YQyMzNdWhecWSwWDR061N1llOitt97S9OnT3bb9jz/+WHv37nU8Rqc/V852+/77791Ws7t9/fXX6ty5s8LCwuTn56f69evr1ltv1bx5885rfS+++KLmzJlTZP7AgQOVk5Ojt99++29WDFRcnu4uAMD5GTt2rOrVq6fc3FwlJyfr+++/14gRIzRx4kR99dVXat26taPt008/rSeffNKl9e/fv1/PPfec6tatqzZt2pT6fgsWLHBpO+fjbLW9++67ys/PL/ca/o4lS5aoU6dOevbZZ0vVvk2bNnrkkUckSQcOHNB7772nAQMGKDs7W/fff3+x9xkzZoy+/vrrUte0c+dOrVmzRnXr1tXMmTM1ZMiQUt3vyJEjat++vTIyMnTvvfeqadOmOnz4sDZu3KgpU6ZoyJAhZw1vqNzeeust1ahRQwMHDnTL9l9++WXdfvvtCg4OliR9+OGHTsv/85//aOHChUXmN2vW7G9t9++8zpzP63FZeeWVV/TYY4+pc+fOGjVqlPz8/LRr1y4tWrRIs2fPVs+ePV1e54svvqibb75Zffv2dZrv4+OjAQMGaOLEiRo2bFiV6IVD1UOQAiqpXr16qX379o7pUaNGacmSJerTp4+uv/56bdu2Tb6+vpIkT09PeXqW79P9xIkT8vPzk7e3d7lu51y8vLzcuv3SSE1NVfPmzUvdvnbt2rrzzjsd0wMHDlT9+vX12muvFRuk2rRpo7lz5+qXX35Ru3btSrWNjz76SGFhYXr11Vd18803a8+ePaU6den9999XUlKSfvzxR1122WVOyzIyMtx+PODitW7dOm3YsEGvvvqqY97pzxNJWrVqlRYuXFhk/pkKX79K6++8zlyI1+Pi5OXlady4cerevXuxH3ilpqaW+TZvvfVWTZgwQUuXLlWXLl3KfP2Au3FqH3AR6dKli5555hn98ccf+uijjxzzizsnf+HChbriiisUEhKigIAANWnSRP/85z8lFZwidumll0qS7rnnHsfpMIWn8Fx99dVq2bKlEhMTddVVV8nPz89x3zOvkSpkt9v1z3/+UxEREfL399f111+vvXv3OrWpW7dusZ9sn77Oc9VW3LULx48f1yOPPKKoqCjZbDY1adJEr7zyiowxTu0KT6OaM2eOWrZsKZvNphYtWpT6lJfU1FQNGjRI4eHh8vHx0SWXXKIZM2Y4lheeerd792598803jtr37NlTqvUXqlmzppo2barffvut2OXDhg1TtWrVNGbMmFKvc9asWbr55pvVp08fBQcHa9asWaW632+//SYPDw916tSpyLKgoCD5+Pg4zVu9erV69uyp4OBg+fn5qXPnzvrxxx+L3PeHH37QpZdeKh8fHzVo0EBvv/12keN4z549Tn/701ksliL7v2/fPt17770KDw93/G0/+OADpzaFf6NPPvlEL7zwgurUqSMfHx917dpVu3btKrKd1atX69prr1W1atXk7++v1q1b6/XXX3dqs337dt18880KDQ2Vj4+P2rdvr6+++qrIus5Xfn6+Jk2apBYtWsjHx0fh4eF64IEHdPToUad2devWVZ8+ffTDDz+oQ4cO8vHxUf369Ys9TXTjxo3q3LmzfH19VadOHT3//POaNm2a0/Fat25dbdmyRcuWLXMcy2c+97OzszVy5EjVrFlT/v7+uvHGG3Xw4EGnNmvXrlV8fLxq1KghX19f1atXT/fee+8593vOnDny9vbWVVdd5dLjdbbXry+//FK9e/dWZGSkbDabGjRooHHjxslutzut48zXmcJj8ZVXXtE777yjBg0ayGaz6dJLL9WaNWuc7lvc67Errz3ff/+92rdvf9bnRnEOHTqkjIwMXX755cUuDwsLc5rOzs7Ws88+q4YNG8pmsykqKkqPP/640zVPFotFx48f14wZMxzHwOmv4bGxsQoNDdWXX3551tqAyooeKeAic9ddd+mf//ynFixYUOJpX1u2bFGfPn3UunVrjR07VjabTbt27XK8oW3WrJnGjh2r0aNHa/DgwbryyislyanH4fDhw+rVq5duv/123XnnnQoPDz9rXS+88IIsFoueeOIJpaamatKkSerWrZvWr1/v6DkrjdLUdjpjjK6//notXbpUgwYNUps2bTR//nw99thj2rdvn1577TWn9j/88IM+//xz/d///Z8CAwP1xhtvqF+/fkpKSlL16tVLrOvkyZO6+uqrtWvXLg0dOlT16tXTp59+qoEDByotLU3Dhw9Xs2bN9OGHH+rhhx9WnTp1HKfr1axZs9T7LxV8svznn3+qWrVqxS4PCgrSww8/rNGjR5eqV2r16tXatWuXpk2bJm9vb910002aOXOm483l2cTExMhut+vDDz/UgAEDztp2yZIl6tWrl2JjY/Xss8/KarVq2rRp6tKli1asWKEOHTpIkjZt2qQePXqoZs2aGjNmjPLy8vTss8+e8xg7m5SUFHXq1MnxhrVmzZr67rvvNGjQIGVkZBQZ8OOll16S1WrVo48+qvT0dE2YMEH9+/fX6tWrHW0WLlyoPn36qFatWho+fLgiIiK0bds2zZ07V8OHD5dU8Fy7/PLLVbt2bT355JPy9/fXJ598or59++p///ufbrzxxvPep0IPPPCApk+frnvuuUcPPfSQdu/erX//+99at26dfvzxR6fek127dunmm2/WoEGDNGDAAH3wwQcaOHCgYmNj1aJFC0kFgfOaa66RxWLRqFGj5O/vr/fee082m81pu5MmTdKwYcMUEBCgp556SpKK/I0KQ/2zzz6rPXv2aNKkSRo6dKj++9//Sir48KHwb/3kk08qJCREe/bs0eeff37O/f7pp5/UsmXL8+odKun1a/r06QoICNDIkSMVEBCgJUuWaPTo0crIyNDLL798zvXOmjVLx44d0wMPPCCLxaIJEybopptu0u+//37OOkvz2rNu3Tr17NlTtWrV0nPPPSe73a6xY8eW6jUkLCxMvr6++vrrrzVs2DCFhoaW2DY/P1/XX3+9fvjhBw0ePFjNmjXTpk2b9Nprr+nXX391XBP14Ycf6r777lOHDh00ePBgSVKDBg2c1tWuXbtiPywBLgoGQKUybdo0I8msWbOmxDbBwcGmbdu2julnn33WnP50f+2114wkc/DgwRLXsWbNGiPJTJs2rciyzp07G0lm6tSpxS7r3LmzY3rp0qVGkqldu7bJyMhwzP/kk0+MJPP666875sXExJgBAwacc51nq23AgAEmJibGMT1nzhwjyTz//PNO7W6++WZjsVjMrl27HPMkGW9vb6d5GzZsMJLMm2++WWRbp5s0aZKRZD766CPHvJycHBMXF2cCAgKc9j0mJsb07t37rOs7vW2PHj3MwYMHzcGDB82mTZvMXXfdZSSZhIQEp7aFj/Wnn35q0tLSTLVq1cz111/v9Nj4+/sX2cbQoUNNVFSUyc/PN8YYs2DBAiPJrFu37pz1JScnm5o1axpJpmnTpubBBx80s2bNMmlpaU7t8vPzTaNGjUx8fLxjO8YYc+LECVOvXj3TvXt3x7y+ffsaHx8f88cffzjmbd261Xh4eDgdx7t37y7xOJBknn32Wcf0oEGDTK1atcyhQ4ec2t1+++0mODjYnDhxwhjz12PYrFkzk52d7Wj3+uuvG0lm06ZNxhhj8vLyTL169UxMTIw5evRokX0t1LVrV9OqVSuTlZXltPyyyy4zjRo1KlJ3cftx5t/5dCtWrDCSzMyZM53mz5s3r8j8mJgYI8ksX77cMS81NdXYbDbzyCOPOOYNGzbMWCwWp7//4cOHTWhoqJFkdu/e7ZjfokULp+dmocLXqW7dujk9Hg8//LDx8PBwHB9ffPHFOV/PSlKnTh3Tr1+/s7ZJSEgwZ77VOdvrV+FxcLoHHnjA+Pn5Of0Nz3ydKTwWq1evbo4cOeKY/+WXXxpJ5uuvv3bMO/P12JjSv/Zcd911xs/Pz+zbt88xb+fOncbT07PIOoszevRoI8n4+/ubXr16mRdeeMEkJiYWaffhhx8aq9VqVqxY4TR/6tSpRpL58ccfHfP8/f2Lfd0uNHjwYOPr63vO2oDKiFP7gItQQEDAWUfvCwkJkVRwGsv5XjBts9l0zz33lLr93XffrcDAQMf0zTffrFq1aunbb789r+2X1rfffisPDw899NBDTvMfeeQRGWP03XffOc3v1q2b0yeqrVu3VlBQkH7//fdzbiciIkJ33HGHY56Xl5ceeughZWZmatmyZee9DwsWLFDNmjVVs2ZNtWrVSh9++KHuueees35CHhwcrBEjRuirr77SunXrSmyXl5en//73v7rtttscpwZ16dJFYWFhmjlz5jlrCw8P14YNG/Tggw/q6NGjmjp1qv7xj38oLCxM48aNc5w+uX79eu3cuVP/+Mc/dPjwYR06dEiHDh3S8ePH1bVrVy1fvlz5+fmy2+2aP3+++vbtq+joaMd2mjVrpvj4+NI+ZE6MMfrf//6n6667TsYYx7YPHTqk+Ph4paen65dffnG6zz333ON0fVdhz2fhcbBu3Trt3r1bI0aMcDyfChU+jkeOHNGSJUt066236tixY45tHj58WPHx8dq5c6f27dt3XvtU6NNPP1VwcLC6d+/utF+xsbEKCAjQ0qVLndo3b97csS9SQW9okyZNnI7vefPmKS4uzmkgl9DQUPXv39/l+gYPHux0ytmVV14pu92uP/74Q9Jfr0Vz585Vbm6uS+s+fPhwib2y51LS69fpveOFf7Mrr7xSJ06c0Pbt28+53ttuu82ppjOPm7M512uP3W7XokWL1LdvX0VGRjraNWzYUL169Trn+iXpueee06xZs9S2bVvNnz9fTz31lGJjY9WuXTtt27bN0e7TTz9Vs2bN1LRpU6fjqvA6pzOPq7OpVq2aTp48qRMnTpT6PkBlQZACLkKZmZlOoeVMt912my6//HLdd999Cg8P1+23365PPvnEpVBVu3ZtlwYSaNSokdO0xWJRw4YNXb4+yFV//PGHIiMjizwehaN2Fb6hK3T6m/dC1apVK3K9SXHbadSokaxW55fVkrbjio4dO2rhwoWaN2+eXnnlFYWEhOjo0aPnfPyHDx+ukJCQs14rtWDBAh08eFAdOnTQrl27tGvXLu3evVvXXHONPv7441IdE7Vq1dKUKVN04MAB7dixQ2+88YZq1qyp0aNH6/3335dUMCqgJA0YMMARCgtv7733nrKzs5Wenq6DBw/q5MmTRY4XSWrSpMk5aynOwYMHlZaWpnfeeafItgvfTJ95of2Zx0Hhm+PC46Dw+rSWLVuWuN1du3bJGKNnnnmmyHYLR2z8uxf479y5U+np6QoLCyuyjczMzHPuV+G+nX58//HHH2rYsGGRdsXNO5dzPY6dO3dWv3799Nxzz6lGjRq64YYbNG3atFJ/95A54zrH0irp9WvLli268cYbFRwcrKCgINWsWdMxUEV6evo513uu/XXlvoX3L7xvamqqTp48+bf/NnfccYdWrFiho0ePasGCBfrHP/6hdevW6brrrlNWVpakguNqy5YtRY6pxo0bO2oprcK/EaP24WLENVLARebPP/9Uenr6Wf+x+vr6avny5Vq6dKm++eYbzZs3T//973/VpUsXLViwQB4eHufcjivXNZVWSf9o7XZ7qWoqCyVt53zfsJWFGjVqqFu3bpKk+Ph4NW3aVH369NHrr7+ukSNHlni/wl6pMWPGlNgrVdjrdOuttxa7fNmyZbrmmmtKVafFYlHjxo3VuHFj9e7dW40aNdLMmTN13333OQLZyy+/XOJw+gEBAS59eefZjpfTFW77zjvvLPE6rtO/LkAqm+OgcLuPPvpoib1p5xNOztzG2XoPz7x25kIf3+faXuGXda9atUpff/215s+fr3vvvVevvvqqVq1addah86tXr16qgFKc4l6/0tLS1LlzZwUFBWns2LFq0KCBfHx89Msvv+iJJ54o1YcKf+fxvdB/m6CgIHXv3l3du3eXl5eXZsyYodWrV6tz587Kz89Xq1atNHHixGLvGxUVVertHD16VH5+fuXyPwNwN4IUcJEp/L6Uc50GZbVa1bVrV3Xt2lUTJ07Uiy++qKeeekpLly5Vt27dyvzTw8IeiULGGO3atcvpDWy1atWUlpZW5L5//PGH6tev75h2pbaYmBgtWrRIx44dc+qVKjxNJyYmptTrOtd2Nm7cqPz8fKdeqbLejiT17t1bnTt31osvvqgHHnhA/v7+JbYdMWKEJk2apOeee67IKWjHjx/Xl19+qdtuu00333xzkfs+9NBDmjlzZqmD1Onq16+vatWq6cCBA5L+ugA9KCjIEQqLU7NmTfn6+hY5XiRpx44dTtOFn/afecyc2ftXs2ZNBQYGym63n3Xbrijcn82bN5e4zsJj1svLq8y2W1wdixYt0uWXX15mb1RjYmKKHaGwuHll9TrRqVMnderUSS+88IJmzZql/v37a/bs2brvvvtKvE/Tpk21e/fuMtm+VDAa3uHDh/X55587jQRYltv4O8LCwuTj41Pqv40r2rdvrxkzZjg9Xzds2KCuXbue8298ruW7d+/+29/bBVRUnNoHXESWLFmicePGqV69eme9nuHIkSNF5hX2EhT2CBS+OS8u2JyP//znP07XbX322Wc6cOCA07n9DRo00KpVq5STk+OYN3fu3CLDpLtS27XXXiu73a5///vfTvNfe+01WSyWUl9bUJrtJCcnO0YjkwquP3rzzTcVEBCgzp07l8l2Cj3xxBM6fPiw3n333bO2K+yV+vLLL7V+/XqnZV988YWOHz+uhIQE3XzzzUVuffr00f/+97+z9hKtXr1ax48fLzL/559/1uHDhx2n48XGxqpBgwZ65ZVXlJmZWaR94ZDYHh4eio+P15w5c5SUlORYvm3bNs2fP9/pPkFBQapRo4aWL1/uNP+tt95ymvbw8FC/fv30v//9T5s3by5x265o166d6tWrp0mTJhU5Dgt7EMLCwnT11Vfr7bffdrxB/bvbPdOtt94qu92ucePGFVmWl5d3Xs/f+Ph4rVy50ul4OXLkSLG9Xv7+/n/rNeLo0aNFelzOfC0qSVxcnDZv3uxSL+bZFPYInV5PTk5OkePJXTw8PNStWzfNmTNH+/fvd8zftWtXkWs9i3PixAmtXLmy2GWF9y98vt56663at29fsa8vJ0+edHrOn+sY+OWXX0ocVRWo7OiRAiqp7777Ttu3b1deXp5SUlK0ZMkSLVy4UDExMfrqq6+KfH/P6caOHavly5erd+/eiomJUWpqqt566y3VqVNHV1xxhaSCUBMSEqKpU6cqMDBQ/v7+6tixo+rVq3de9YaGhuqKK67QPffco5SUFE2aNEkNGzZ0GqL9vvvu02effaaePXvq1ltv1W+//aaPPvqoyHC6rtR23XXX6ZprrtFTTz2lPXv26JJLLtGCBQv05ZdfasSIEUXWfb4GDx6st99+WwMHDlRiYqLq1q2rzz77TD/++KMmTZp01mvWzkevXr3UsmVLTZw4UQkJCWcdWnn48OF67bXXtGHDBqfeq5kzZ6p69eolvsm5/vrr9e677+qbb77RTTfdVGybDz/8UDNnztSNN96o2NhYeXt7a9u2bfrggw/k4+PjGELdarXqvffeU69evdSiRQvdc889ql27tvbt26elS5cqKChIX3/9taSCC+LnzZunK6+8Uv/3f//nCKQtWrTQxo0bnbZ/33336aWXXtJ9992n9u3ba/ny5fr111+L1PnSSy9p6dKl6tixo+6//341b95cR44c0S+//KJFixYV++HC2VitVk2ZMkXXXXed2rRpo3vuuUe1atXS9u3btWXLFkfomzx5sq644gq1atVK999/v+rXr6+UlBStXLlSf/75pzZs2HDOba1du1bPP/98kflXX321OnfurAceeEDjx4/X+vXr1aNHD3l5eWnnzp369NNP9frrrxfb23g2jz/+uD766CN1795dw4YNcwx/Hh0drSNHjjj1QMTGxmrKlCl6/vnn1bBhQ4WFhbn0xaszZszQW2+9pRtvvFENGjTQsWPH9O677yooKEjXXnvtWe97ww03aNy4cVq2bJl69Ojh0j4W57LLLlO1atU0YMAAPfTQQ7JYLPrwww/delrvmcaMGaMFCxbo8ssv15AhQxwfErVs2bLIByVnOnHihC677DJ16tRJPXv2VFRUlNLS0jRnzhytWLFCffv2Vdu2bSUVfI3GJ598ogcffFBLly7V5ZdfLrvdru3bt+uTTz7R/PnzHV8IHxsbq0WLFmnixImKjIxUvXr11LFjR0lSYmKijhw5ohtuuKFcHxfAbS70MIEA/p7CYYULb97e3iYiIsJ0797dvP76607DbBc6c7jdxYsXmxtuuMFERkYab29vExkZae644w7z66+/Ot3vyy+/NM2bN3cMrVs4zHTnzp1NixYtiq2vpOHPP/74YzNq1CgTFhZmfH19Te/evZ2Gty706quvmtq1axubzWYuv/xys3bt2iLrPFttZw5LbIwxx44dMw8//LCJjIw0Xl5eplGjRubll192GpbZmJKHmi5pWPYzpaSkmHvuucfUqFHDeHt7m1atWhU7NLerw5+X1Hb69OlO+3768OdnKjwGCoc/T0lJMZ6enuauu+4qcdsnTpwwfn5+5sYbbyyxzcaNG81jjz1m2rVrZ0JDQ42np6epVauWueWWW8wvv/xSpP26devMTTfdZKpXr25sNpuJiYkxt956q1m8eLFTu2XLlpnY2Fjj7e1t6tevb6ZOnVrssNEnTpwwgwYNMsHBwSYwMNDceuutJjU1tcjw54X7nJCQYKKiooyXl5eJiIgwXbt2Ne+8846jTUmPYUlDrf/www+me/fuJjAw0Pj7+5vWrVsXGSr/t99+M3fffbeJiIgwXl5epnbt2qZPnz7ms88+K/FxLXT6c/3M27hx4xzt3nnnHRMbG2t8fX1NYGCgadWqlXn88cfN/v37HW1KOpaKe36tW7fOXHnllcZms5k6deqY8ePHmzfeeMNIMsnJyY52ycnJpnfv3iYwMNBIcqynpK9pKHx8ly5daowx5pdffjF33HGHiY6ONjabzYSFhZk+ffqYtWvXnvOxMcaY1q1bm0GDBpW4vKThz0t6/frxxx9Np06djK+vr4mMjDSPP/64mT9/vlPNxpQ8/PnLL79cZJ1nHoslDX9e2teexYsXm7Zt2xpvb2/ToEED895775lHHnnE+Pj4lPAoFMjNzTXvvvuu6du3r4mJiTE2m834+fmZtm3bmpdfftlpuH9jCr6+4V//+pdp0aKFsdlsplq1aiY2NtY899xzJj093dFu+/bt5qqrrjK+vr5GklO9TzzxhImOji7yWgtcLCzGVKCPWgAAKMGYMWP03HPPVagegqpkxIgRevvtt5WZmXnBBn85lw8//FAJCQlKSkoqcg1gVdK3b19t2bKl2GsL3SU7O1t169bVk08+6fiCauBiwzVSAADAycmTJ52mDx8+rA8//FBXXHFFhQlRktS/f39FR0dr8uTJ7i7lgjnzb7Nz5059++23uvrqq91TUAmmTZsmLy8vPfjgg+4uBSg39EgBACoFeqQunDZt2ujqq69Ws2bNlJKSovfff1/79+/X4sWLnUa0w4VXq1YtDRw4UPXr19cff/yhKVOmKDs7W+vWrSv2+9cAlB8GmwAAAE6uvfZaffbZZ3rnnXdksVjUrl07vf/++4SoCqBnz576+OOPlZycLJvNpri4OL344ouEKMAN6JECAAAAABdxjRQAAAAAuIggBQAAAAAu4hopSfn5+dq/f78CAwOdvmgQAAAAQNVijNGxY8cUGRkpq7XkfieClKT9+/crKirK3WUAAAAAqCD27t2rOnXqlLicICUpMDBQUsGDFRQU5OZqAAAAALhLRkaGoqKiHBmhJAQpyXE6X1BQEEEKAAAAwDkv+WGwCQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABc5OnuAlBUUlKSDh06VC7rrlGjhqKjo8tl3QAAAEBVQZCqYJKSktS0WTOdPHGiXNbv6+en7du2EaYAAACAv4EgVcEcOnRIJ0+cUP8nXlZ4dIMyXXdK0m+a+a/HdOjQIYIUAAAA8DcQpCqo8OgGqtOohbvLAAAAAFAMBpsAAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFzk1iBVt25dWSyWIreEhARJUlZWlhISElS9enUFBASoX79+SklJcVpHUlKSevfuLT8/P4WFhemxxx5TXl6eO3YHAAAAQBXh1iC1Zs0aHThwwHFbuHChJOmWW26RJD388MP6+uuv9emnn2rZsmXav3+/brrpJsf97Xa7evfurZycHP3000+aMWOGpk+frtGjR7tlfwAAAABUDW4NUjVr1lRERITjNnfuXDVo0ECdO3dWenq63n//fU2cOFFdunRRbGyspk2bpp9++kmrVq2SJC1YsEBbt27VRx99pDZt2qhXr14aN26cJk+erJycHHfuGgAAAICLWIW5RionJ0cfffSR7r33XlksFiUmJio3N1fdunVztGnatKmio6O1cuVKSdLKlSvVqlUrhYeHO9rEx8crIyNDW7ZsKXFb2dnZysjIcLoBAAAAQGlVmCA1Z84cpaWlaeDAgZKk5ORkeXt7KyQkxKldeHi4kpOTHW1OD1GFywuXlWT8+PEKDg523KKiospuRwAAAABc9CpMkHr//ffVq1cvRUZGlvu2Ro0apfT0dMdt79695b5NAAAAABcPT3cXIEl//PGHFi1apM8//9wxLyIiQjk5OUpLS3PqlUpJSVFERISjzc8//+y0rsJR/QrbFMdms8lms5XhHgAAAACoSipEj9S0adMUFham3r17O+bFxsbKy8tLixcvdszbsWOHkpKSFBcXJ0mKi4vTpk2blJqa6mizcOFCBQUFqXnz5hduBwAAAABUKW7vkcrPz9e0adM0YMAAeXr+VU5wcLAGDRqkkSNHKjQ0VEFBQRo2bJji4uLUqVMnSVKPHj3UvHlz3XXXXZowYYKSk5P19NNPKyEhgR4nAAAAAOXG7UFq0aJFSkpK0r333ltk2WuvvSar1ap+/fopOztb8fHxeuuttxzLPTw8NHfuXA0ZMkRxcXHy9/fXgAEDNHbs2Au5CwAAAACqGLcHqR49esgYU+wyHx8fTZ48WZMnTy7x/jExMfr222/LqzwAAAAAKKJCXCMFAAAAAJUJQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARW4PUvv27dOdd96p6tWry9fXV61atdLatWsdy40xGj16tGrVqiVfX19169ZNO3fudFrHkSNH1L9/fwUFBSkkJESDBg1SZmbmhd4VAAAAAFWEW4PU0aNHdfnll8vLy0vfffedtm7dqldffVXVqlVztJkwYYLeeOMNTZ06VatXr5a/v7/i4+OVlZXlaNO/f39t2bJFCxcu1Ny5c7V8+XINHjzYHbsEAAAAoArwdOfG//WvfykqKkrTpk1zzKtXr57jd2OMJk2apKefflo33HCDJOk///mPwsPDNWfOHN1+++3atm2b5s2bpzVr1qh9+/aSpDfffFPXXnutXnnlFUVGRl7YnQIAAABw0XNrj9RXX32l9u3b65ZbblFYWJjatm2rd99917F89+7dSk5OVrdu3RzzgoOD1bFjR61cuVKStHLlSoWEhDhClCR169ZNVqtVq1evLna72dnZysjIcLoBAAAAQGm5NUj9/vvvmjJliho1aqT58+dryJAheuihhzRjxgxJUnJysiQpPDzc6X7h4eGOZcnJyQoLC3Na7unpqdDQUEebM40fP17BwcGOW1RUVFnvGgAAAICLmFuDVH5+vtq1a6cXX3xRbdu21eDBg3X//fdr6tSp5brdUaNGKT093XHbu3dvuW4PAAAAwMXFrUGqVq1aat68udO8Zs2aKSkpSZIUEREhSUpJSXFqk5KS4lgWERGh1NRUp+V5eXk6cuSIo82ZbDabgoKCnG4AAAAAUFpuDVKXX365duzY4TTv119/VUxMjKSCgSciIiK0ePFix/KMjAytXr1acXFxkqS4uDilpaUpMTHR0WbJkiXKz89Xx44dL8BeAAAAAKhq3Dpq38MPP6zLLrtML774om699Vb9/PPPeuedd/TOO+9IkiwWi0aMGKHnn39ejRo1Ur169fTMM88oMjJSffv2lVTQg9WzZ0/HKYG5ubkaOnSobr/9dkbsAwAAAFAu3BqkLr30Un3xxRcaNWqUxo4dq3r16mnSpEnq37+/o83jjz+u48ePa/DgwUpLS9MVV1yhefPmycfHx9Fm5syZGjp0qLp27Sqr1ap+/frpjTfecMcuAQAAAKgC3BqkJKlPnz7q06dPicstFovGjh2rsWPHltgmNDRUs2bNKo/yAAAAAKAIt14jBQAAAACVEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEVuDVJjxoyRxWJxujVt2tSxPCsrSwkJCapevboCAgLUr18/paSkOK0jKSlJvXv3lp+fn8LCwvTYY48pLy/vQu8KAAAAgCrE090FtGjRQosWLXJMe3r+VdLDDz+sb775Rp9++qmCg4M1dOhQ3XTTTfrxxx8lSXa7Xb1791ZERIR++uknHThwQHfffbe8vLz04osvXvB9AQAAAFA1uD1IeXp6KiIiosj89PR0vf/++5o1a5a6dOkiSZo2bZqaNWumVatWqVOnTlqwYIG2bt2qRYsWKTw8XG3atNG4ceP0xBNPaMyYMfL29r7QuwMAAACgCnD7NVI7d+5UZGSk6tevr/79+yspKUmSlJiYqNzcXHXr1s3RtmnTpoqOjtbKlSslSStXrlSrVq0UHh7uaBMfH6+MjAxt2bKlxG1mZ2crIyPD6QYAAAAApeXWINWxY0dNnz5d8+bN05QpU7R7925deeWVOnbsmJKTk+Xt7a2QkBCn+4SHhys5OVmSlJyc7BSiCpcXLivJ+PHjFRwc7LhFRUWV7Y4BAAAAuKi59dS+Xr16OX5v3bq1OnbsqJiYGH3yySfy9fUtt+2OGjVKI0eOdExnZGQQpgAAAACUmttP7TtdSEiIGjdurF27dikiIkI5OTlKS0tzapOSkuK4pioiIqLIKH6F08Vdd1XIZrMpKCjI6QYAAAAApVWhglRmZqZ+++031apVS7GxsfLy8tLixYsdy3fs2KGkpCTFxcVJkuLi4rRp0yalpqY62ixcuFBBQUFq3rz5Ba8fAAAAQNXg1lP7Hn30UV133XWKiYnR/v379eyzz8rDw0N33HGHgoODNWjQII0cOVKhoaEKCgrSsGHDFBcXp06dOkmSevTooebNm+uuu+7ShAkTlJycrKeffloJCQmy2Wzu3DUAAAAAFzG3Bqk///xTd9xxhw4fPqyaNWvqiiuu0KpVq1SzZk1J0muvvSar1ap+/fopOztb8fHxeuuttxz39/Dw0Ny5czVkyBDFxcXJ399fAwYM0NixY921SwAAAACqALcGqdmzZ591uY+PjyZPnqzJkyeX2CYmJkbffvttWZcGAAAAACWqUNdIAQAAAEBlQJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARecVpOrXr6/Dhw8XmZ+Wlqb69ev/7aIAAAAAoCI7ryC1Z88e2e32IvOzs7O1b9++v10UAAAAAFRknq40/uqrrxy/z58/X8HBwY5pu92uxYsXq27dumVWHAAAAABURC4Fqb59+0qSLBaLBgwY4LTMy8tLdevW1auvvlpmxQEAAABAReRSkMrPz5ck1atXT2vWrFGNGjXKpSgAAAAAqMhcClKFdu/eXdZ1AAAAAEClcV5BSpIWL16sxYsXKzU11dFTVeiDDz7424UBAAAAQEV1XkHqueee09ixY9W+fXvVqlVLFoulrOsCAAAAgArrvILU1KlTNX36dN11111lXQ8AAAAAVHjn9T1SOTk5uuyyy8q0kJdeekkWi0UjRoxwzMvKylJCQoKqV6+ugIAA9evXTykpKU73S0pKUu/eveXn56ewsDA99thjysvLK9PaAAAAAOB05xWk7rvvPs2aNavMilizZo3efvtttW7d2mn+ww8/rK+//lqffvqpli1bpv379+umm25yLLfb7erdu7dycnL0008/acaMGZo+fbpGjx5dZrUBAAAAwJnO69S+rKwsvfPOO1q0aJFat24tLy8vp+UTJ04s9boyMzPVv39/vfvuu3r++ecd89PT0/X+++9r1qxZ6tKliyRp2rRpatasmVatWqVOnTppwYIF2rp1qxYtWqTw8HC1adNG48aN0xNPPKExY8bI29v7fHYPAAAAAM7qvHqkNm7cqDZt2shqtWrz5s1at26d47Z+/XqX1pWQkKDevXurW7duTvMTExOVm5vrNL9p06aKjo7WypUrJUkrV65Uq1atFB4e7mgTHx+vjIwMbdmypcRtZmdnKyMjw+kGAAAAAKV1Xj1SS5cuLZONz549W7/88ovWrFlTZFlycrK8vb0VEhLiND88PFzJycmONqeHqMLlhctKMn78eD333HN/s3oAAAAAVdV59UiVhb1792r48OGaOXOmfHx8Lui2R40apfT0dMdt7969F3T7AAAAACq38+qRuuaaa8763VFLliw55zoSExOVmpqqdu3aOebZ7XYtX75c//73vzV//nzl5OQoLS3NqVcqJSVFERERkqSIiAj9/PPPTustHNWvsE1xbDabbDbbOWsEAAAAgOKcV5Bq06aN03Rubq7Wr1+vzZs3a8CAAaVaR9euXbVp0yaneffcc4+aNm2qJ554QlFRUfLy8tLixYvVr18/SdKOHTuUlJSkuLg4SVJcXJxeeOEFpaamKiwsTJK0cOFCBQUFqXnz5uezawAAAABwTucVpF577bVi548ZM0aZmZmlWkdgYKBatmzpNM/f31/Vq1d3zB80aJBGjhyp0NBQBQUFadiwYYqLi1OnTp0kST169FDz5s111113acKECUpOTtbTTz+thIQEepwAAAAAlJsyvUbqzjvv1AcffFBm63vttdfUp08f9evXT1dddZUiIiL0+eefO5Z7eHho7ty58vDwUFxcnO68807dfffdGjt2bJnVAAAAAABnOq8eqZKsXLnybw0c8f333ztN+/j4aPLkyZo8eXKJ94mJidG333573tsEAAAAAFedV5C66aabnKaNMTpw4IDWrl2rZ555pkwKAwAAAICK6ryCVHBwsNO01WpVkyZNNHbsWPXo0aNMCgMAAACAiuq8gtS0adPKug4AAAAAqDT+1jVSiYmJ2rZtmySpRYsWatu2bZkUBQAAAAAV2XkFqdTUVN1+++36/vvvHV+Wm5aWpmuuuUazZ89WzZo1y7JGAAAAAKhQzmv482HDhunYsWPasmWLjhw5oiNHjmjz5s3KyMjQQw89VNY1AgAAAECFcl49UvPmzdOiRYvUrFkzx7zmzZtr8uTJDDYBAAAA4KJ3Xj1S+fn58vLyKjLfy8tL+fn5f7soAAAAAKjIzitIdenSRcOHD9f+/fsd8/bt26eHH35YXbt2LbPiAAAAAKAiOq8g9e9//1sZGRmqW7euGjRooAYNGqhevXrKyMjQm2++WdY1AgAAAECFcl7XSEVFRemXX37RokWLtH37dklSs2bN1K1btzItDgAAAAAqIpd6pJYsWaLmzZsrIyNDFotF3bt317BhwzRs2DBdeumlatGihVasWFFetQIAAABAheBSkJo0aZLuv/9+BQUFFVkWHBysBx54QBMnTiyz4gAAAACgInIpSG3YsEE9e/YscXmPHj2UmJj4t4sCAAAAgIrMpSCVkpJS7LDnhTw9PXXw4MG/XRQAAAAAVGQuBanatWtr8+bNJS7fuHGjatWq9beLAgAAAICKzKUgde211+qZZ55RVlZWkWUnT57Us88+qz59+pRZcQAAAABQEbk0/PnTTz+tzz//XI0bN9bQoUPVpEkTSdL27ds1efJk2e12PfXUU+VSKAAAAABUFC4FqfDwcP30008aMmSIRo0aJWOMJMlisSg+Pl6TJ09WeHh4uRQKAAAAABWFy1/IGxMTo2+//VZHjx7Vrl27ZIxRo0aNVK1atfKoDwAAAAAqHJeDVKFq1arp0ksvLctaAAAAAKBScGmwCQAAAAAAQQoAAAAAXEaQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcBFBCgAAAABcRJACAAAAABcRpAAAAADARQQpAAAAAHARQQoAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFzk6e4CUDrZeXYt2paq7Fy7okP9FFPdXzUCvGWxWNxdGgAAAFDlEKQqgZO5ds1Zt0+px7IlSXuPntSPvx1WgM1TPZqHKyrUz80VAgAAAFULp/ZVcMez8/S/X/5U6rFs+Xp56PIG1VW3up88rRZlZudp3pZkncy1u7tMAAAAoEqhR6oCy8zO0+e//KmjJ3Ll7+2hG9vWVvUAm9pLyrXna/bPe3XkRI6W7Tioni0j3F0uAAAAUGXQI1WBfb8jVUdP5CrQx1M3x9ZR9QCbY5mXh1Xdm4fLImlHyjHtSs10X6EAAABAFUOQqqAycqXfDh6XJN1wSaRC/LyLtIkI9lH7utUkSUu2p+pETt4FrREAAACoqghSFdSvGR6SpAY1/Z16os7UoV6oqvt762SuXd/vOHihygMAAACqNIJUBeQRWF1Jxwv+NO1jQs/a1tNqVY/m4bJYpJ2pmTp4amQ/AAAAAOWHIFUBBbXvKyOLaof4KiLY55ztw4J81KBmgCRpy/708i4PAAAAqPLcGqSmTJmi1q1bKygoSEFBQYqLi9N3333nWJ6VlaWEhARVr15dAQEB6tevn1JSUpzWkZSUpN69e8vPz09hYWF67LHHlJdXea8VOpadr4A2PSXJcf1TabSIDJIkbU8+pjx7frnUBgAAAKCAW4NUnTp19NJLLykxMVFr165Vly5ddMMNN2jLli2SpIcfflhff/21Pv30Uy1btkz79+/XTTfd5Li/3W5X7969lZOTo59++kkzZszQ9OnTNXr0aHft0t8277fjsnr7KtgrXzEufNFudKifAmyeys7LdwxSAQAAAKB8uDVIXXfddbr22mvVqFEjNW7cWC+88IICAgK0atUqpaen6/3339fEiRPVpUsXxcbGatq0afrpp5+0atUqSdKCBQu0detWffTRR2rTpo169eqlcePGafLkycrJyXHnrp2Xkzl2fbPzhCSpcVC+LBZLqe9rtVjU/FSvFKf3AQAAAOWrwlwjZbfbNXv2bB0/flxxcXFKTExUbm6uunXr5mjTtGlTRUdHa+XKlZKklStXqlWrVgoPD3e0iY+PV0ZGhqNXqzjZ2dnKyMhwulUEP+46pIzsfOWlJauOn+un57WoVRCk9h49qfSTuWVdHgAAAIBT3B6kNm3apICAANlsNj344IP64osv1Lx5cyUnJ8vb21shISFO7cPDw5WcnCxJSk5OdgpRhcsLl5Vk/PjxCg4OdtyioqLKdqfOU7fm4XotvoYOz3tT1tJ3RjkE+XopKtRXkrR1f8UIhwAAAMDFyO1BqkmTJlq/fr1Wr16tIUOGaMCAAdq6dWu5bnPUqFFKT0933Pbu3Vuu23NFTLCXsv7YcN73bxkZLEnaeiBD+caUVVkAAAAATuPp7gK8vb3VsGFDSVJsbKzWrFmj119/XbfddptycnKUlpbm1CuVkpKiiIgISVJERIR+/vlnp/UVjupX2KY4NptNNlvJX3JbmdWv6S8fT6sys/OUdPiE6tbwd3dJAAAAwEXH7T1SZ8rPz1d2drZiY2Pl5eWlxYsXO5bt2LFDSUlJiouLkyTFxcVp06ZNSk1NdbRZuHChgoKC1Lx58wtee0XgabWqacSpodBTjrm5GgAAAODi5NYeqVGjRqlXr16Kjo7WsWPHNGvWLH3//feaP3++goODNWjQII0cOVKhoaEKCgrSsGHDFBcXp06dOkmSevTooebNm+uuu+7ShAkTlJycrKeffloJCQkXbY9TaTQMC9D6P9P0x+HjyjdGVhdG/wMAAABwbm4NUqmpqbr77rt14MABBQcHq3Xr1po/f766d+8uSXrttddktVrVr18/ZWdnKz4+Xm+99Zbj/h4eHpo7d66GDBmiuLg4+fv7a8CAARo7dqy7dqlCqBXsI29Pq7Jy85Waka2IYB93lwQAAABcVNwapN5///2zLvfx8dHkyZM1efLkEtvExMTo22+/LevSKjWr1aKYUD/tTM3U7sPHCVIAAABAGatw10ihbMRU95Mk/XH4uJsrAQAAAC4+BKmLVN3qBaP1pWRk60ROnpurAQAAAC4uBKmLlL/NUzUDCwbc+OPwCTdXAwAAAFxcCFIXsbqnTu/bw+l9AAAAQJkiSF3ECk/vSzp8QvnGuLkaAAAA4OJBkLqIRQT5yOZpVVZevlIystxdDgAAAHDRIEhdxAqHQZekPYe4TgoAAAAoKwSpi1xMjYLT+7hOCgAAACg7BKmLXGGPVOqxbGXZ3VwMAAAAcJEgSF3k/G2eqhHgLUk6lG1xczUAAADAxYEgVQXUDvGVJB3K4s8NAAAAlAXeWVcBjiBFjxQAAABQJghSVUDkqSCVnmuRxebv5moAAACAyo8gVQX42zwV4uslySKf2s3cXQ4AAABQ6RGkqoja1Qp6pWxRLd1cCQAAAFD5EaSqiMLT+2xRLdxcCQAAAFD5EaSqiMIBJ2wRjZSdZ9xcDQAAAFC5EaSqiCAfT/l6GFk8PPXrkRx3lwMAAABUagSpKsJisaiGLV+StPUgQQoAAAD4OwhSVUh1W8EpfdsIUgAAAMDfQpCqQmqeClI7Ducq157v5moAAACAyosgVYUEehnZT2Yo2260eV+6u8sBAAAAKi2CVBVisUjZe7dIkn7efcTN1QAAAACVF0Gqisn6c6skac0eghQAAABwvghSVUz2vm2SpHVJaTKG75MCAAAAzgdBqorJSflNnlbp8PEc/Xn0pLvLAQAAAColglRVY89VvRAvSdIvSUfdXAwAAABQORGkqqDG1QuC1LqkNPcWAgAAAFRSBKkqqHF1b0nSur1p7i0EAAAAqKQIUlVQo9CCHqmt+9OVlWt3czUAAABA5UOQqoLC/T1U3d9buXajLfsz3F0OAAAAUOkQpKogi8WittEhkqT1nN4HAAAAuIwgVUW1ja4mSVrHyH0AAACAywhSVVTbqBBJjNwHAAAAnA+CVBXVqk6wLBZpX9pJpWZkubscAAAAoFIhSFVRgT5eahwWKIlh0AEAAABXEaSqsMIBJzi9DwAAAHANQaoK+2vkPgacAAAAAFxBkKrCCkfu2/hnuvLs+W6uBgAAAKg8CFJVWIOaAQqweepEjl07Uo65uxwAAACg0iBIVWEeVota1wmWVNArBQAAAKB0CFJVXJtT3ye1ngEnAAAAgFIjSFVxl5wKUhv+THNrHQAAAEBlQpCq4gp7pH5NOabj2XnuLQYAAACoJAhSVVx4kI8ignyUb6TN+7hOCgAAACgNghR0SVTBgBOc3gcAAACUDkEKahNV8H1SG/bSIwUAAACUBkEKjh6p9XvT3FsIAAAAUEm4NUiNHz9el156qQIDAxUWFqa+fftqx44dTm2ysrKUkJCg6tWrKyAgQP369VNKSopTm6SkJPXu3Vt+fn4KCwvTY489prw8Bk4orVa1g2WxSPvSTurgsWx3lwMAAABUeG4NUsuWLVNCQoJWrVqlhQsXKjc3Vz169NDx48cdbR5++GF9/fXX+vTTT7Vs2TLt379fN910k2O53W5X7969lZOTo59++kkzZszQ9OnTNXr0aHfsUqUU6OOlhjUDJEkb6JUCAAAAzsnTnRufN2+e0/T06dMVFhamxMREXXXVVUpPT9f777+vWbNmqUuXLpKkadOmqVmzZlq1apU6deqkBQsWaOvWrVq0aJHCw8PVpk0bjRs3Tk888YTGjBkjb29vd+xapXNJVIh2pmZqw59p6tY83N3lAAAAABVahbpGKj29YLCD0NBQSVJiYqJyc3PVrVs3R5umTZsqOjpaK1eulCStXLlSrVq1Unj4X2/+4+PjlZGRoS1bthS7nezsbGVkZDjdqrrCL+blOikAAADg3CpMkMrPz9eIESN0+eWXq2XLlpKk5ORkeXt7KyQkxKlteHi4kpOTHW1OD1GFywuXFWf8+PEKDg523KKiosp4byqfNnVCJBWc2meMcW8xAAAAQAVXYYJUQkKCNm/erNmzZ5f7tkaNGqX09HTHbe/eveW+zYquaa1AeXtalZGVpz2HT7i7HAAAAKBCqxBBaujQoZo7d66WLl2qOnXqOOZHREQoJydHaWlpTu1TUlIUERHhaHPmKH6F04VtzmSz2RQUFOR0q+q8PKxqGVnwODDgBAAAAHB2bg1SxhgNHTpUX3zxhZYsWaJ69eo5LY+NjZWXl5cWL17smLdjxw4lJSUpLi5OkhQXF6dNmzYpNTXV0WbhwoUKCgpS8+bNL8yOXCS4TgoAAAAoHbeO2peQkKBZs2bpyy+/VGBgoOOapuDgYPn6+io4OFiDBg3SyJEjFRoaqqCgIA0bNkxxcXHq1KmTJKlHjx5q3ry57rrrLk2YMEHJycl6+umnlZCQIJvN5s7dq3TaEKQAAACAUnFrkJoyZYok6eqrr3aaP23aNA0cOFCS9Nprr8lqtapfv37Kzs5WfHy83nrrLUdbDw8PzZ07V0OGDFFcXJz8/f01YMAAjR079kLtxkXjklMDTmzdn6GcvHx5e1aIMz8BAACACsetQao0o8P5+Pho8uTJmjx5coltYmJi9O2335ZlaVVSTHU/hfh5Ke1ErrYnZ6j1qWAFAAAAwBldDnCwWCyOXikGnAAAAABKRpCCk78GnEh3byEAAABABUaQgpM2UcGSpA1/prm3EAAAAKACI0jBSeF1Ub8dzFRGVq57iwEAAAAqKIIUnNQIsKlONV8ZI23+k9P7AAAAgOIQpFBE4XVS6xhwAgAAACgWQQpFtD0VpBi5DwAAACgeQQpFFPZIMeAEAAAAUDyCFIpoERkkD6tFKRnZSk7Pcnc5AAAAQIVDkEIRft6eahweKElaz+l9AAAAQBEEKRSL75MCAAAASkaQQrEuOfV9Ugw4AQAAABRFkEKxCgec2Phnuuz5xr3FAAAAABUMQQrFahweKD9vD2Vm5+n3g5nuLgcAAACoUAhSKJaH1aKWtQuuk1qXlObeYgAAAIAKhiCFErWLriZJ+iXpqJsrAQAAACoWghRK1C46RBJBCgAAADgTQQolahdT0CP1a0qm0k/murkaAAAAoOIgSKFENQJsiqnuJ4kv5gUAAABOR5DCWcWeuk4q8Q9O7wMAAAAKEaRwVoWn963jOikAAADAgSCFsyocuW9dUhpfzAsAAACcQpDCWTWJCJT/qS/m3Zl6zN3lAAAAABUCQQpn5WG1qM2pYdC5TgoAAAAoQJDCOTHgBAAAAOCMIIVzahvz13VSAAAAAAhSKIV2UQVBaveh4zqcme3magAAAAD3I0jhnIL9vNQwLEASvVIAAACARJBCKTmuk+L7pAAAAACCFEqnXUyIJAacAAAAACSCFEopNiZUkrRhb5qy8+xurgYAAABwL4IUSqVBTX/VCPBWdl6+Nv6Z7u5yAAAAALciSKFULBaLOtQr6JVa/fthN1cDAAAAuBdBCqXWsV51SdLq3UfcXAkAAADgXgQplFrH+gU9Uol/HFWuPd/N1QAAAADuQ5BCqTUOC1SIn5dO5Ni1aR/XSQEAAKDqIkih1KxWizrULbxOitP7AAAAUHURpOCSwgEnft7NgBMAAACoughScEmn+gUDTqzdc1T2fOPmagAAAAD3IEjBJc1qBSnQx1PHsvO0dX+Gu8sBAAAA3IIgBZd4WC26tPA6KU7vAwAAQBVFkILLOp66TmoVA04AAACgiiJIwWUdT10ntWbPEeVznRQAAACqIIIUXNYyMkh+3h5KP5mr7cnH3F0OAAAAcMERpOAyTw+r2p+6Tmrl71wnBQAAgKqHIIXzckXDgtP7lv960M2VAAAAABceQQrn5arGNSUVjNyXlWt3czUAAADAhUWQwnlpEh6o8CCbsnLztWYPo/cBAACganFrkFq+fLmuu+46RUZGymKxaM6cOU7LjTEaPXq0atWqJV9fX3Xr1k07d+50anPkyBH1799fQUFBCgkJ0aBBg5SZmXkB96JqslgsuqpRQa8Up/cBAACgqnFrkDp+/LguueQSTZ48udjlEyZM0BtvvKGpU6dq9erV8vf3V3x8vLKyshxt+vfvry1btmjhwoWaO3euli9frsGDB1+oXajSCk/vW0aQAgAAQBXj6c6N9+rVS7169Sp2mTFGkyZN0tNPP60bbrhBkvSf//xH4eHhmjNnjm6//XZt27ZN8+bN05o1a9S+fXtJ0ptvvqlrr71Wr7zyiiIjIy/YvlRFVzSsIYtF+jUlUwfST6pWsK+7SwIAAAAuiAp7jdTu3buVnJysbt26OeYFBwerY8eOWrlypSRp5cqVCgkJcYQoSerWrZusVqtWr15d4rqzs7OVkZHhdIPrqvl7q3WdEEnSil8PubcYAAAA4AKqsEEqOTlZkhQeHu40Pzw83LEsOTlZYWFhTss9PT0VGhrqaFOc8ePHKzg42HGLiooq4+qrjs6Fp/ft5PQ+AAAAVB0VNkiVp1GjRik9Pd1x27t3r7tLqrQ6N64hSfph5yHZ842bqwEAAAAujAobpCIiIiRJKSkpTvNTUlIcyyIiIpSamuq0PC8vT0eOHHG0KY7NZlNQUJDTDefnkjohCvTxVPrJXG34M83d5QAAAAAXRIUNUvXq1VNERIQWL17smJeRkaHVq1crLi5OkhQXF6e0tDQlJiY62ixZskT5+fnq2LHjBa+5KvL0sOqKhgW9UgyDDgAAgKrCrUEqMzNT69ev1/r16yUVDDCxfv16JSUlyWKxaMSIEXr++ef11VdfadOmTbr77rsVGRmpvn37SpKaNWumnj176v7779fPP/+sH3/8UUOHDtXtt9/OiH0XUOEw6AQpAAAAVBVuHf587dq1uuaaaxzTI0eOlCQNGDBA06dP1+OPP67jx49r8ODBSktL0xVXXKF58+bJx8fHcZ+ZM2dq6NCh6tq1q6xWq/r166c33njjgu9LVVY44MS6vWk6eCxbNQNtbq4IAAAAKF9uDVJXX321jCl5gAKLxaKxY8dq7NixJbYJDQ3VrFmzyqM8lFJkiK9a1wnWxj/TtWBrsvp3jHF3SQAAAEC5qrDXSKFy6dmyYHCPeZtLHnYeAAAAuFgQpFAmerWsJUla+dthpZ3IcXM1AAAAQPkiSKFM1Kvhr6YRgcrLN1q4NeXcdwAAAAAqMYIUygyn9wEAAKCqIEihzBSe3rdi5yEdy8p1czUAAABA+SFIocw0Dg9Q/Rr+yrHna8n2VHeXAwAAAJQbghTKjMViUa9WnN4HAACAix9BCmWq8PS+pTtSdSInz83VAAAAAOWDIIUy1SIySHWq+SorN1/Ldhx0dzkAAABAuSBIoUxZLBb1blXQK/W/X/a5uRoAAACgfBCkUOZuaV9HUsHpfakZWW6uBgAAACh7BCmUuYZhgYqNqSZ7vqFXCgAAABclghTKxW3toyRJn6zdK2OMm6sBAAAAyhZBCuWid+ta8vf20O5Dx/Xz7iPuLgcAAAAoUwQplAt/m6euuyRSkvTfNXvdXA0AAABQtghSKDe3XVpwet+3mw8oIyvXzdUAAAAAZYcghXLTJipEjcMDlJWbr6/W73d3OQAAAECZIUih3FgsFt12abQkafaaJDdXAwAAAJQdghTK1Y1ta8vb06rN+zK0+vfD7i4HAAAAKBMEKZSrUH9v3RJb8AW9k7//zc3VAAAAAGWDIIVy92DnBvKwWrT814Pa+Geau8sBAAAA/jaCFMpdVKifbjg1FPpbS+mVAgAAQOVHkMIFMeTqBpKkeVuStTPlmJurAQAAAP4eghQuiEbhgYpvES5JmsK1UgAAAKjkCFK4YBKuaShJ+nLDfiUdPuHmagAAAIDz5+nuAnDhbdu2rdzWXaNGDUVHRxe7rHWdEF3ZqIZW7DykSYt+1cTb2pRbHQAAAEB5IkhVIRlHDkqS7rzzznLbhq+fn7Zv21ZimHqkRxP9sOuQPl+3T3d0jNaldUPLrRYAAACgvBCkqpCTmRmSpN4PPKUmrWPLfP0pSb9p5r8e06FDh0oMUm2iQnT7pVH6+Oe9embOZs0ddoU8PTjDFAAAAJULQaoKqh4ZozqNWrht+4/FN9V3m5O1PfmYPlz1h+65vJ7bagEAAADOB10BuOBC/b31eHxTSdLEBb8q9ViWmysCAAAAXEOQglvcdmmULqkTrGPZeRr/7XZ3lwMAAAC4hCAFt/CwWjT2hpayWKQv1u3Tt5sOuLskAAAAoNQIUnCbS6JCNPiq+pKkxz7doN8OZrq5IgAAAKB0CFJwq8d6NFHHeqE6nmPXgx8m6nh2nrtLAgAAAM6JIAW38vSw6s1/tFVYoE07UzP1zy82yRjj7rIAAACAsyJIwe3CAn00uX87eVgt+nL9fr274nd3lwQAAACcFUEKFcKldUM1qlfBkOgvfrtd7xGmAAAAUIERpFBhDLqinhKuaSBJev6bbYQpAAAAVFgEKVQYFotFj/Zoooe6NJRUEKamLvvNzVUBAAAARRGkUKFYLBaN7NFEw7s2kiS99N12jfxkvU7kMJofAAAAKg6CFCqkh7s31j+vbSqrRfr8l3267s0ftCP5mLvLAgAAACQRpFCBDb6qgT6+v5PCg2z67eBxXf/vH/Teit+Vk5fv7tIAAABQxRGkUKF1rF9d3z50pa5qXFPZefl6/pttip+0XPO3JPN9UwAAAHAbghQqvOoBNk0feKlevLGVagR4a/eh43rgw0Td+vZKzd+SrDw7PVQAAAC4sDzdXQAuPtu2bSuX9V5Rq4auf+waTfl+l95bsVtr9hzVmj2Jqh3iq390jNbNsXUUHuRTLtsGAAAATkeQQpnJOHJQknTnnXeWy/p9/fy0fds2PRbfVP07xmjGyj36ZM1e7Us7qZfn79DL83fokqgQ9Wgeru7Nw9UoLEAWi6VcagEkKSkpSYcOHSqXddeoUUPR0dHlsm4AAPD3EaRQZk5mZkiSej/wlJq0ji3Tdack/aaZ/3pMhw4dUnR0tCJDfDWqVzM93K2xvtl4QB//nKTEpKPasDdNG/am6eX5OxTq7632MdV0ad1QtYkOUZOIQAX5eJVpXai6kpKS1LRZM508caJc1l/4wQFhCgCAiokghTJXPTJGdRq1uCDb8vHyUL/YOuoXW0epGVlatC1VC7Yma+Vvh3XkeI4WbE3Rgq0pjvZ1qvmqSXigoqv7Kaqan6JC/RQV6quoan7yt/F0QOkdOnRIJ0+cUP8nXlZ4dIMyXfeZHxwA+HvoPQZQHi6ad46TJ0/Wyy+/rOTkZF1yySV688031aFDB3eXhTJ2ruuvmnpJTS/x1JCWYfr9aK62HcrRtkM52p2Wq0Mn8vXn0ZP68+jJYu8b7OOhmBoBqhlgU/UAb1UPsKm6v7dqBNhUI8CmUH9vBfl6KtDmpQAfT3lYOW2wKsjPNzqek6fM7Dwdyyq4ZWbnaePekwpo3UPpIY2U4xGmnLz8v272gp/ZeXbl5RsZIxljlH9qoMl8UzBPkqxWyWqxyGqxyMNqkdUi2b2aKGLAJD2x6JBCfv5JnlarvD0LbjbHTw/ZTk2fPu/MNt4ltDmcmqxj6Ufl7WGRl9UiT6vK7JjmjSUuhDx7wXMtOzdf2aeee3ZjTnuuFfzcv/+ArrvhemVn5UgWyWKxFoz6mm+XMXbJbpfJtxdM59ul/Lwzpu1nrYPeY6DquiiC1H//+1+NHDlSU6dOVceOHTVp0iTFx8drx44dCgsLc3d5KANlcf2V1eYvr7B68qoRLc/g8L9uIeHy8A1SepZdG/9ML/X6/Lw9FOjjqQCbpwJ9vE773VN+3p7y8fI47c2rVTav09/4esjmVfC7p9Uqq0WynPZG2mqxyGLRqem/5hXeLBbJarXIIslikSyynPqpgjcKp01bLM7tCt5InNrGGfdXMetzun8lueYsJy9fJ3PtOplj14mcPJ3Isetkrl0ncuw6np2nzKw8HcvO07GsXGVmnRaSsvOUmZXrCEuZWXnKzMlTSSPtV+/1kDalSUo7cv7FFvsezSpbREPtPJIrHTl6/ut2kcm3y9jzTr2BzDv1BvPUm0p7bjHL84rMM/Y8eVikW2++SSFBQfL0sMjbwypPD4s8rVZ5eVjk6WGVp9XieH54e1rl7eFx2u/OobFw2mm5h1VWPsxwC2OM8vLNqQ8LCj4wcPyee8Z0nv3Uz8Lldqfw49z2zOVnb2vPL/1XYFT/x6t/Z4/lYZE8LZKH42bkYZXs2Se0f+dmjfp6pyLDjsrHy0O+3h7y8bTKx9tDPp4e8vHykI+X9YyfhctOm/ayysfTg+P6IlQY7u35Rvmn/UNx/F8+9bv01//dwt8d7SrI/99z9e4W7quRZIxO/W4cv5/Oesb7jho1qqteTLQ8PSrPoOIXRZCaOHGi7r//ft1zzz2SpKlTp+qbb77RBx98oCeffNLN1aEslOf1V5K0L2mHPv/g33rj/Y8UEl5Hh4/n6FBmtg5l5uhwZrYOZ+bo8PFsZWTlOb4Q+EROwRvzFGWXeT0VVUGg019hT87Txf9e8jIPi0U2by/5+fk4emQ8LBZZT/20GyN7fsGbNnt+vvLsp0//NT/Xbv4KTDkFvUBlzdNqUaBPQWgOsHlKeVla+9MKNbmkvUKqhTq96T89IJwelJ3C6al/IPn5p/7BGnPqd6OUP/fof5Of12uT3lBM3XrKPfWmNeeMN5NnTp8+z3k6Xzmn5uXk5etEdo4yjp+U1cumv/5VSxarhyxWjzJ5vL7eelRS+YZAr1MhrUjIKuyJ8zhzvvO0l8epDycKj7tTH1D81TtYMM/Dain4oOP0350+9HDtDc75vB0q7Gmx5xf0aOafOl7sRo7jxn6qp7PwzZrTsWWM8uxGufZ85drzlZP31++59nzl2I1y8wp/LzhOCpb91S4rt+D1Ly+/6Bsid/Ow6NTfU6d9QFTwXLPn5SkjPV3+QSHy8PKURZa/3tideowcj2mx+2WR3Uh24zyvQID8GnXSj3uzpL1/lsm+eFklbw+L42bz8pSvzVseVou8PAqOPU+rteCnY7rofOtpx+Xpx9zph+vx48eVlZVV4nKn14dil/+luA+cbD4+CggIOL3VWe9T3DrMue5TijrMqedH4XPDnl/wXCpufn6+dCIrS9k5uacdGyrm94LaTp8+W9uyVnicnz4tnf6/xuKY6RTIShHcTrur0/8ruz1f6RkZcnwia7HIYrGe+t1aBv8/kjW40yH9s2/7v7meC6fSB6mcnBwlJiZq1KhRjnlWq1XdunXTypUri71Pdna2srP/evObnl7QC5GRkVG+xZZCZmamJOnPnVuUfbJsL2JPSfpNkpS851f95u9Xpusu7/UXrjs3J7vMHxdJMtknlHtwtzyStygyOE+RHpKCT90cbJJsyrMbnbQbnczN14lcFfzMK5g+mWt0Is8o69SbjVy7Ucbxk1r2w0/Kt1hl8fSWxcNLFk8vWTy8ZfH0PPUi5HHqBangxUhWq6TCFygVzLNYT73YWSSrtWCZ49XOctqr4qn7lYPK9o1dVotk87TI5mGRzaPgd19Pi3y8rPLztMjX0ypfL4v8vCzy8bTKz8si31PLfDwtp6Yt8vWwysvj9E8EjXbs2KPvPh+na6LHqaZ/PSnHedtGUvapm6ty/9ytrN8T5ZG8Rf7BeUUbWCR5nbqV/tFQ4VcH7tixW4MHD9YtI8apRu16f/2zV8HNmMKfloJ5jjcNp3/CKOWfWm5Ou//h5D+1bc0Pslg9JY9TwczDUxarp+N3WayyeHhKHl4FzwcPj1M/C27y8DzteeIpi9XLMX268318UbaMPVcmL1fGniNjz/vr97xcmfxTy/JyZey50qllsueeapvjWFZ0PTmnzftr2Zn3lT1XRd9OF9XngX8qplHzs+9L4em3p343KghQdkn2/L8CVf6pn8l//qENPy4+dazaZPHylsXTu+BDCo+C3y2e3qfm2wpe9728ZPG0yeppK7jfqf8LhTiuUbGd+j9YcM66zGnvDMoiLx49eLBCvB8vrMGUdErKKRZzrhYV3P79+1W7dm399NNPiouLc8x//PHHtWzZMq1evbrIfcaMGaPnnnvuQpYJAAAAoBLZu3ev6tSpU+LySt8jdT5GjRqlkSNHOqbz8/N15MgRVa9e3a3noGZkZCgqKkp79+5VUFCQ2+oAJI5HVCwcj6hoOCZRkXA8li1jjI4dO6bIyMiztqv0QapGjRry8PBQSkqK0/yUlBRFREQUex+bzSabzeY0LyQkpLxKdFlQUBBPAlQYHI+oSDgeUdFwTKIi4XgsO8HBwedsU3mGxSiBt7e3YmNjtXjxYse8/Px8LV682OlUPwAAAAAoK5W+R0qSRo4cqQEDBqh9+/bq0KGDJk2apOPHjztG8QMAAACAsnRRBKnbbrtNBw8e1OjRo5WcnKw2bdpo3rx5Cg8Pd3dpLrHZbHr22WeLnHYIuAPHIyoSjkdUNByTqEg4Ht2j0o/aBwAAAAAXWqW/RgoAAAAALjSCFAAAAAC4iCAFAAAAAC4iSAEAAACAiwhSFcjkyZNVt25d+fj4qGPHjvr555/dXRIqueXLl+u6665TZGSkLBaL5syZ47TcGKPRo0erVq1a8vX1Vbdu3bRz506nNkeOHFH//v0VFBSkkJAQDRo0SJmZmU5tNm7cqCuvvFI+Pj6KiorShAkTynvXUAmNHz9el156qQIDAxUWFqa+fftqx44dTm2ysrKUkJCg6tWrKyAgQP369SvyhetJSUnq3bu3/Pz8FBYWpscee0x5eXlObb7//nu1a9dONptNDRs21PTp08t791DJTJkyRa1bt3Z8gWlcXJy+++47x3KORbjTSy+9JIvFohEjRjjmcUxWQAYVwuzZs423t7f54IMPzJYtW8z9999vQkJCTEpKirtLQyX27bffmqeeesp8/vnnRpL54osvnJa/9NJLJjg42MyZM8ds2LDBXH/99aZevXrm5MmTjjY9e/Y0l1xyiVm1apVZsWKFadiwobnjjjscy9PT0014eLjp37+/2bx5s/n444+Nr6+vefvtty/UbqKSiI+PN9OmTTObN28269evN9dee62Jjo42mZmZjjYPPvigiYqKMosXLzZr1641nTp1MpdddpljeV5enmnZsqXp1q2bWbdunfn2229NjRo1zKhRoxxtfv/9d+Pn52dGjhxptm7dat58803j4eFh5s2bd0H3FxXbV199Zb755hvz66+/mh07dph//vOfxsvLy2zevNkYw7EI9/n5559N3bp1TevWrc3w4cMd8zkmKx6CVAXRoUMHk5CQ4Ji22+0mMjLSjB8/3o1V4WJyZpDKz883ERER5uWXX3bMS0tLMzabzXz88cfGGGO2bt1qJJk1a9Y42nz33XfGYrGYffv2GWOMeeutt0y1atVMdna2o80TTzxhmjRpUs57hMouNTXVSDLLli0zxhQcf15eXubTTz91tNm2bZuRZFauXGmMKfhwwGq1muTkZEebKVOmmKCgIMcx+Pjjj5sWLVo4beu2224z8fHx5b1LqOSqVatm3nvvPY5FuM2xY8dMo0aNzMKFC03nzp0dQYpjsmLi1L4KICcnR4mJierWrZtjntVqVbdu3bRy5Uo3VoaL2e7du5WcnOx03AUHB6tjx46O427lypUKCQlR+/btHW26desmq9Wq1atXO9pcddVV8vb2drSJj4/Xjh07dPTo0Qu0N6iM0tPTJUmhoaGSpMTEROXm5jodk02bNlV0dLTTMdmqVSunL1yPj49XRkaGtmzZ4mhz+joK2/B6ipLY7XbNnj1bx48fV1xcHMci3CYhIUG9e/cuctxwTFZMnu4uANKhQ4dkt9udDnxJCg8P1/bt291UFS52ycnJklTscVe4LDk5WWFhYU7LPT09FRoa6tSmXr16RdZRuKxatWrlUj8qt/z8fI0YMUKXX365WrZsKangePH29lZISIhT2zOPyeKO2cJlZ2uTkZGhkydPytfXtzx2CZXQpk2bFBcXp6ysLAUEBOiLL75Q8+bNtX79eo5FXHCzZ8/WL7/8ojVr1hRZxutjxUSQAgBccAkJCdq8ebN++OEHd5eCKqxJkyZav3690tPT9dlnn2nAgAFatmyZu8tCFbR3714NHz5cCxculI+Pj7vLQSlxal8FUKNGDXl4eBQZeSUlJUURERFuqgoXu8Jj62zHXUREhFJTU52W5+Xl6ciRI05tilvH6dsATjd06FDNnTtXS5cuVZ06dRzzIyIilJOTo7S0NKf2Zx6T5zreSmoTFBTEp61w4u3trYYNGyo2Nlbjx4/XJZdcotdff51jERdcYmKiUlNT1a5dO3l6esrT01PLli3TG2+8IU9PT4WHh3NMVkAEqQrA29tbsbGxWrx4sWNefn6+Fi9erLi4ODdWhotZvXr1FBER4XTcZWRkaPXq1Y7jLi4uTmlpaUpMTHS0WbJkifLz89WxY0dHm+XLlys3N9fRZuHChWrSpAmn9cGJMUZDhw7VF198oSVLlhQ5JTQ2NlZeXl5Ox+SOHTuUlJTkdExu2rTJKeAvXLhQQUFBat68uaPN6esobMPrKc4lPz9f2dnZHIu44Lp27apNmzZp/fr1jlv79u3Vv39/x+8ckxWQu0e7QIHZs2cbm81mpk+fbrZu3WoGDx5sQkJCnEZeAVx17Ngxs27dOrNu3TojyUycONGsW7fO/PHHH8aYguHPQ0JCzJdffmk2btxobrjhhmKHP2/btq1ZvXq1+eGHH0yjRo2chj9PS0sz4eHh5q677jKbN282s2fPNn5+fgx/jiKGDBligoODzffff28OHDjguJ04ccLR5sEHHzTR0dFmyZIlZu3atSYuLs7ExcU5lhcO79ujRw+zfv16M2/ePFOzZs1ih/d97LHHzLZt28zkyZMZ3hdFPPnkk2bZsmVm9+7dZuPGjebJJ580FovFLFiwwBjDsQj3O33UPmM4JisiglQF8uabb5ro6Gjj7e1tOnToYFatWuXuklDJLV261EgqchswYIAxpmAI9GeeecaEh4cbm81munbtanbs2OG0jsOHD5s77rjDBAQEmKCgIHPPPfeYY8eOObXZsGGDueKKK4zNZjO1a9c2L7300oXaRVQixR2Lksy0adMcbU6ePGn+7//+z1SrVs34+fmZG2+80Rw4cMBpPXv27DG9evUyvr6+pkaNGuaRRx4xubm5Tm2WLl1q2rRpY7y9vU39+vWdtgEYY8y9995rYmJijLe3t6lZs6bp2rWrI0QZw7EI9zszSHFMVjwWY4xxT18YAAAAAFROXCMFAAAAAC4iSAEAAACAiwhSAAAAAOAighQAAAAAuIggBQAAAAAuIkgBAAAAgIsIUgAAAADgIoIUAAAAALiIIAUAwEXMYrFozpw57i4DAC46BCkAwFkdPHhQQ4YMUXR0tGw2myIiIhQfH68ff/zR3aVVGBUhrIwZM0Zt2rRxaw0AUJV4ursAAEDF1q9fP+Xk5GjGjBmqX7++UlJStHjxYh0+fNjdpQEA4Db0SAEASpSWlqYVK1boX//6l6655hrFxMSoQ4cOGjVqlK6//nqndvfdd59q1qypoKAgdenSRRs2bHBa10svvaTw8HAFBgZq0KBBevLJJ516UK6++mqNGDHC6T59+/bVwIEDHdPZ2dl69NFHVbt2bfn7+6tjx476/vvvHcunT5+ukJAQzZ8/X82aNVNAQIB69uypAwcOOK33gw8+UIsWLWSz2VSrVi0NHTrUpX1x1XvvvadmzZrJx8dHTZs21VtvveVYtmfPHlksFn3++ee65ppr5Ofnp0suuUQrV650Wse7776rqKgo+fn56cYbb9TEiRMVEhLi2O/nnntOGzZskMVikcVi0fTp0x33PXTokG688Ub5+fmpUaNG+uqrr/7W/gAACFIAgLMICAhQQECA5syZo+zs7BLb3XLLLUpNTdV3332nxMREtWvXTl27dtWRI0ckSZ988onGjBmjF198UWvXrlWtWrWcwkRpDR06VCtXrtTs2bO1ceNG3XLLLerZs6d27tzpaHPixAm98sor+vDDD7V8+XIlJSXp0UcfdSyfMmWKEhISNHjwYG3atElfffWVGjZsWOp9cdXMmTM1evRovfDCC9q2bZtefPFFPfPMM5oxY4ZTu6eeekqPPvqo1q9fr8aNG+uOO+5QXl6eJOnHH3/Ugw8+qOHDh2v9+vXq3r27XnjhBcd9b7vtNj3yyCNq0aKFDhw4oAMHDui2225zLH/uued06623auPGjbr22mvVv3//894fAMApBgCAs/jss89MtWrVjI+Pj7nsssvMqFGjzIYNGxzLV6xYYYKCgkxWVpbT/Ro0aGDefvttY4wxcXFx5v/+7/+clnfs2NFccskljunOnTub4cOHO7W54YYbzIABA4wxxvzxxx/Gw8PD7Nu3z6lN165dzahRo4wxxkybNs1IMrt27XIsnzx5sgkPD3dMR0ZGmqeeeqrYfS3NvhRHkvniiy+KXdagQQMza9Ysp3njxo0zcXFxxhhjdu/ebSSZ9957z7F8y5YtRpLZtm2bMcaY2267zfTu3dtpHf379zfBwcGO6Weffdbp8Ty9tqefftoxnZmZaSSZ7777rsT9AQCcGz1SAICz6tevn/bv36+vvvpKPXv21Pfff6927do5Th3bsGGDMjMzVb16dUcPVkBAgHbv3q3ffvtNkrRt2zZ17NjRab1xcXEu1bFp0ybZ7XY1btzYaTvLli1zbEeS/Pz81KBBA8d0rVq1lJqaKklKTU3V/v371bVr12K3UZp9ccXx48f122+/adCgQU7re/7554usr3Xr1k41F9YrSTt27FCHDh2c2p85fTanr9vf319BQUGOdQMAzg+DTQAAzsnHx0fdu3dX9+7d9cwzz+i+++7Ts88+q4EDByozM1O1atVyulapUOE1PKVhtVpljHGal5ub6/g9MzNTHh4eSkxMlIeHh1O7gIAAx+9eXl5OyywWi2O9vr6+Z62hrPbl9PVJBdc3nRkkz9yH0+u2WCySpPz8fJe3WZziHpOyWjcAVFUEKQCAy5o3b+4Y7rtdu3ZKTk6Wp6en6tatW2z7Zs2aafXq1br77rsd81atWuXUpmbNmk6DQtjtdm3evFnXXHONJKlt27ay2+1KTU3VlVdeeV51BwYGqm7dulq8eLFjvacrzb64Ijw8XJGRkfr999/Vv3//815PkyZNtGbNGqd5Z057e3vLbref9zYAAK4hSAEASnT48GHdcsstuvfee9W6dWsFBgZq7dq1mjBhgm644QZJUrdu3RQXF6e+fftqwoQJaty4sfbv369vvvlGN954o9q3b6/hw4dr4MCBat++vS6//HLNnDlTW7ZsUf369R3b6tKli0aOHKlvvvlGDRo00MSJE5WWluZY3rhxY/Xv31933323Xn31VbVt21YHDx7U4sWL1bp1a/Xu3btU+zRmzBg9+OCDCgsLU69evXTs2DH9+OOPGjZsWKn2pSS7d+/W+vXrneY1atRIzz33nB566CEFBwerZ8+eys7O1tq1a3X06FGNHDmyVDUPGzZMV111lSZOnKjrrrtOS5Ys0XfffefouZKkunXrOmqoU6eOAgMDZbPZSrV+AMB5cPdFWgCAiisrK8s8+eSTpl27diY4ONj4+fmZJk2amKefftqcOHHC0S4jI8MMGzbMREZGGi8vLxMVFWX69+9vkpKSHG1eeOEFU6NGDRMQEGAGDBhgHn/8cafBEXJycsyQIUNMaGioCQsLM+PHj3cabKKwzejRo03dunWNl5eXqVWrlrnxxhvNxo0bjTEFg02cPgCDMcZ88cUX5sx/d1OnTjVNmjRxrGPYsGEu7cuZJBV7W7FihTHGmJkzZ5o2bdoYb29vU61aNXPVVVeZzz//3Bjz12AT69atc6zv6NGjRpJZunSpY94777xjateubXx9fU3fvn3N888/byIiIpz+Vv369TMhISFGkpk2bZqjtjMHwggODnYsBwCcH4sxZ5yQDgDABTBmzBjNmTOnSC8OSuf+++/X9u3btWLFCneXAgBVEqf2AQBQCbzyyivq3r27/P399d1332nGjBnn9V1cAICyQZACAKAS+PnnnzVhwgQdO3ZM9evX1xtvvKH77rvP3WUBQJXFqX0AAAAA4CK+kBcAAAAAXESQAgAAAAAXEaQAAAAAwEUEKQAAAABwEUEKAAAAAFxEkAIAAAAAFxGkAAAAAMBFBCkAAAAAcNH/A7+TlNKdlQRwAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate sequence lengths\n", "train_seq['seq_length'] = train_seq['sequence'].str.len()\n", "\n", "# Plot histogram\n", "plt.figure(figsize=(10, 6))\n", "sns.histplot(train_seq['seq_length'], bins=30, kde=True)\n", "plt.title('Distribution of RNA Sequence Lengths (Training Set)')\n", "plt.xlabel('Sequence Length')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 11, "id": "db11b79c-0d60-4381-ae6d-6cf77698162d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Convert temporal_cutoff to datetime\n", "train_seq['temporal_cutoff'] = pd.to_datetime(train_seq['temporal_cutoff'])\n", "\n", "# Group by year and count\n", "time_counts = train_seq['temporal_cutoff'].dt.year.value_counts().sort_index()\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 6))\n", "time_counts.plot(kind='bar')\n", "plt.title('Number of Sequences by Publication Year')\n", "plt.xlabel('Year')\n", "plt.ylabel('Number of Sequences')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "633beefb-6359-4704-b0f5-a38e38a38acb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Aggregate nucleotide counts\n", "nucleotides = {'A': 0, 'C': 0, 'G': 0, 'U': 0}\n", "for seq in train_seq['sequence']:\n", "    for char in seq:\n", "        if char in nucleotides:\n", "            nucleotides[char] += 1\n", "\n", "# Plot bar chart\n", "plt.figure(figsize=(8, 5))\n", "plt.bar(nucleotides.keys(), nucleotides.values())\n", "plt.title('Nucleotide Composition in Training Sequences')\n", "plt.xlabel('Nucleotide')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "be93556d-bf6b-4263-a5ad-cb68e896e75a", "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3D scatter of coordinates for a single target\n", "target_id = train_labels['ID'].str.split('_').str[0].iloc[0]  # First target\n", "target_data = train_labels[train_labels['ID'].str.startswith(target_id)]\n", "\n", "fig = plt.figure(figsize=(10, 8))\n", "ax = fig.add_subplot(111, projection='3d')\n", "ax.scatter(target_data['x_1'], target_data['y_1'], target_data['z_1'], c='b', label='Conformation 1')\n", "ax.set_title(f'3D Structure of {target_id}')\n", "ax.set_xlabel('X (Å)')\n", "ax.set_ylabel('Y (Å)')\n", "ax.set_zlabel('<PERSON> (Å)')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f973becb-9794-496d-81f8-11a18fd7d3c1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 15, "id": "429e0489-4196-448d-be93-136da6f33da6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot x-coordinate along residues for a single target\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(target_data['resid'], target_data['x_1'], label='X Coordinate', marker='o')\n", "plt.title(f'X Coordinate Variation Along Residues for {target_id}')\n", "plt.xlabel('Residue Number')\n", "plt.ylabel('X Coordinate (Å)')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ec49d581-e7c0-4db6-b13d-2bce05ddf3d4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c3f14710-e826-4103-8463-18cbedcaa2b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a085519e-45b9-4ee6-8bb2-edafa07d97ce", "metadata": {}, "outputs": [], "source": ["\n", "i want you make diagram in this style and method \n", "\n", "## Diagram Generation Pattern Prompt\n", " \n", "`**Core Concept:** [WORD/CONCEPT] **Primary Domain:** [LINGUISTIC/ETC] **Dual Aspect 1:** [LITERAL/EXPLICIT MEANING] **Dual Aspect 2:** [METAPHORICAL/IMPLICIT MEANING] **Contextual Nodes for Aspect 1:** - [Example 1 with frequency/symbol ➤ 표준어 사용 비율 72%] - [Example 2 with political weight ➤ 헌재 임명 압박 68%] **Cultural Nodes for Aspect 2:** - [Cultural reference ➤ 사과=화해상징 82%] - [Strategic maneuver ➤ 깜짝 경제 행보] **Conflict Points:** [Parameter1 vs Parameter2] e.g., 언어학적 해석 vs 정치적 현실 **Source Anchors:** [Relevant source numbers from provided materials ➤ [3][7]]`\n", "\n", "## Example Application (사과 + 한덕수 사례)\n", "\n", "text\n", "\n", "`**Core Concept:** 사과 **Primary Domain:** Korean Homonym **Dual Aspect 1:** Apple (과일) **Dual Aspect 2:** Apology (정치적 사죄) **Contextual Nodes for Aspect 1:** - 사과주스 생산 증대 ➤ 경제안보 TF 73% - 교육상징 ➤ SK하이닉스 방문 68% **Cultural Nodes for Aspect 2:** - 헌재 임명거부 ➤ 탄핵 리스크 82% [4][7] - 역대급 계엄 사죄 ➤ 해병대 특검법 91% [3] **Conflict Points:** 과일 수확 vs 사죄 수확 (농협계 정치화) [7] **Source Anchors:** [1][3][4][7]`\n", "\n", "## Technical Implementation Guide\n", "\n", "## 1. Mermaid Syntax Rules\n", " \n", "\n", "`graph TD A[\"{{Core Concept}}\"] --> B[\"{{Dual Aspect 1}}\"] A --> C[\"{{Dual Aspect 2}}\"] subgraph \"Contextual {{Aspect 1}}\" B --> D1[\"{{Example 1}}\"] B --> D2[\"{{Example 2}}\"] end subgraph \"Idiomatic {{Aspect 2}}\" C --> E1[\"{{Cultural Ref 1}}\"] C --> E2[\"{{Literal  2}}\"] end`\n", "\n", "## Semantic Weight Parameters\n", "  \n", "`def calculate_weight(text_corpus): return { 'literal_freq': text_corpus.count(ASPECT1_KEYWORD)/len(text_corpus), 'metaphorical_impact': 1 - (text_corpus.count('탄핵')/text_corpus.count('대행')) }`\n", "\n", "from this text , but also provide both korean and english translation\n", "\n", "특히 로버의 사양이나 디자인이 긴급하게 변경될 경우, 이에 맞는 부품이 부족하여 구매 리스크에 시달릴 수도 있다.\n", " "]}, {"cell_type": "code", "execution_count": null, "id": "9efd5c78-0049-486f-b14f-69679de5c242", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "21fb7400-b723-4616-8187-fa8233c4502a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "01f94e90-6a8a-45e1-bde6-ffd8864d2271", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:copick]", "language": "python", "name": "conda-env-copick-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}