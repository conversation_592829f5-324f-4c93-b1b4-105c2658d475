#!/usr/bin/env python3
"""
Test script for Production RNA 3D Structure Prediction Pipeline
Validates integration between existing pipeline and TemporalAwareRibonanzaNet.
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock
import torch
import pandas as pd
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from production_rna_pipeline import (
    ProductionConfig, HybridRNAModel, ProductionPipeline,
    ProductionMonitor, EnsemblePredictor, ProductionValidator
)

class TestProductionPipeline(unittest.TestCase):
    """Test cases for production RNA pipeline."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = ProductionConfig()
        self.config.data_path = self.temp_dir
        self.config.model_save_path = os.path.join(self.temp_dir, "models")
        self.config.results_path = os.path.join(self.temp_dir, "results")
        self.config.seq_length = 64  # Smaller for testing
        self.config.batch_size = 2
        self.config.epochs = 2
        
        # Create test directories
        os.makedirs(self.config.model_save_path, exist_ok=True)
        os.makedirs(self.config.results_path, exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "MSA"), exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_production_config(self):
        """Test production configuration."""
        # Test default configuration
        config = ProductionConfig()
        self.assertIsInstance(config.device, torch.device)
        self.assertEqual(config.num_predictions, 5)
        self.assertTrue(config.competition_mode)
        
        # Test config save/load
        config_path = os.path.join(self.temp_dir, "test_config.json")
        config.save_to_file(config_path)
        self.assertTrue(os.path.exists(config_path))
        
        # Load config
        new_config = ProductionConfig(config_path)
        self.assertEqual(new_config.num_predictions, config.num_predictions)
    
    def test_hybrid_rna_model(self):
        """Test hybrid RNA model initialization and forward pass."""
        model = HybridRNAModel(self.config)
        self.assertIsInstance(model, torch.nn.Module)
        
        # Test forward pass
        batch_size = 2
        seq_len = self.config.seq_length
        
        # Create dummy input
        sequence = torch.randn(batch_size, 6, seq_len)  # 6 channels
        msa = sequence  # Simplified MSA
        temporal_cutoff = 2023  # Year as integer
        
        # Forward pass
        with torch.no_grad():
            output = model(sequence, msa, temporal_cutoff)
            
        # Check output shape
        expected_shape = (batch_size, seq_len, self.config.num_predictions, 3)
        self.assertEqual(output.shape, expected_shape)
        
        # Test with confidence
        with torch.no_grad():
            output, confidence = model(sequence, msa, temporal_cutoff, return_confidence=True)
            
        self.assertEqual(output.shape, expected_shape)
        self.assertEqual(confidence.shape, (batch_size, seq_len))
    
    def test_production_monitor(self):
        """Test production monitoring functionality."""
        monitor = ProductionMonitor(self.config)
        
        # Start monitoring
        monitor.start_monitoring()
        self.assertIsNotNone(monitor.start_time)
        
        # Log GPU memory (if available)
        monitor.log_gpu_memory()
        if torch.cuda.is_available():
            self.assertGreater(len(monitor.metrics['gpu_memory_usage']), 0)
        
        # Log training metrics
        monitor.log_training_metrics(1, 0.5, 0.6, 0.7)
        self.assertEqual(len(monitor.metrics['training_metrics']['epochs']), 1)
        
        # Save metrics
        metrics_path = os.path.join(self.temp_dir, "test_metrics.json")
        monitor.save_metrics(metrics_path)
        self.assertTrue(os.path.exists(metrics_path))
    
    def test_ensemble_predictor(self):
        """Test ensemble prediction functionality."""
        # Create multiple models
        model1 = HybridRNAModel(self.config)
        model2 = HybridRNAModel(self.config)
        
        ensemble = EnsemblePredictor([model1, model2], weights=[0.6, 0.4])
        
        # Test prediction
        batch_size = 1
        seq_len = self.config.seq_length
        sequence = torch.randn(batch_size, 6, seq_len)
        msa = sequence
        temporal_cutoff = 2023  # Year as integer
        
        with torch.no_grad():
            ensemble_pred = ensemble.predict(sequence, msa, temporal_cutoff)
        
        expected_shape = (batch_size, seq_len, self.config.num_predictions, 3)
        self.assertEqual(ensemble_pred.shape, expected_shape)
    
    def test_production_validator(self):
        """Test production validation functionality."""
        validator = ProductionValidator(self.config)
        
        # Create mock data loader
        class MockDataLoader:
            def __init__(self, config):
                self.batch_size = 2
                self.config = config

            def __iter__(self):
                # Return one batch for testing
                features = torch.randn(2, 6, self.config.seq_length)
                targets = torch.randn(2, self.config.seq_length, 5, 3)
                yield features, targets

            def __len__(self):
                return 1
        
        train_loader = MockDataLoader(self.config)
        val_loader = MockDataLoader(self.config)
        
        # Test data quality validation
        data_validation = validator.validate_data_quality(train_loader, val_loader)
        self.assertIn('data_quality', data_validation)
        self.assertIn('issues', data_validation)
        
        # Test model output validation
        model = HybridRNAModel(self.config)
        sample_input = next(iter(train_loader))
        
        model_validation = validator.validate_model_output(model, sample_input)
        self.assertIn('model_output', model_validation)
        self.assertIn('output_stats', model_validation)
    
    @patch('production_rna_pipeline.load_data')
    def test_production_pipeline_integration(self, mock_load_data):
        """Test full production pipeline integration."""
        # Mock data loading
        dummy_seqs = ["ACGU" * 16] * 10  # Small sequences for testing
        dummy_labels = pd.DataFrame()
        sample_submission = pd.DataFrame({'ID': [f'RNA_{i}' for i in range(5)]})
        
        mock_load_data.return_value = (
            dummy_seqs[:8], dummy_seqs[8:9], dummy_seqs[9:], 
            dummy_labels, dummy_labels, sample_submission
        )
        
        # Initialize pipeline
        pipeline = ProductionPipeline(self.config)
        
        # Test data loading
        train_loader, val_loader, test_loader = pipeline.load_and_prepare_data()
        self.assertIsNotNone(train_loader)
        self.assertIsNotNone(val_loader)
        self.assertIsNotNone(test_loader)
        
        # Test model initialization
        pipeline.initialize_model()
        self.assertIsNotNone(pipeline.model)
        self.assertIsNotNone(pipeline.optimizer)
        
        # Test prediction (skip training for speed)
        submission_df = pipeline.predict(test_loader)
        self.assertIsInstance(submission_df, pd.DataFrame)
        self.assertGreater(len(submission_df), 0)
    
    def test_fallback_implementations(self):
        """Test that fallback implementations work correctly."""
        # Test fallback RNADataset
        from production_rna_pipeline import RNADataset, custom_collate_fn
        
        dataset = RNADataset(["ACGU"] * 5, pd.DataFrame(), "", max_len=32)
        self.assertEqual(len(dataset), 5)
        
        # Test data loading
        features, targets = dataset[0]
        self.assertEqual(features.shape, (6, 32))
        
        # Test collate function
        batch = [dataset[i] for i in range(2)]
        batched_features, batched_targets = custom_collate_fn(batch)
        self.assertEqual(batched_features.shape, (2, 6, 32))

class TestProductionPipelineEnd2End(unittest.TestCase):
    """End-to-end integration tests."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = ProductionConfig()
        self.config.data_path = self.temp_dir
        self.config.model_save_path = os.path.join(self.temp_dir, "models")
        self.config.results_path = os.path.join(self.temp_dir, "results")
        self.config.seq_length = 32  # Very small for fast testing
        self.config.batch_size = 1
        self.config.epochs = 1
        
        os.makedirs(self.config.model_save_path, exist_ok=True)
        os.makedirs(self.config.results_path, exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('production_rna_pipeline.load_data')
    def test_minimal_pipeline_run(self, mock_load_data):
        """Test minimal pipeline run with fallback implementations."""
        # Mock minimal data
        dummy_seqs = ["ACGU" * 8] * 3
        dummy_labels = pd.DataFrame()
        sample_submission = pd.DataFrame({'ID': ['RNA_0']})
        
        mock_load_data.return_value = (
            dummy_seqs[:2], dummy_seqs[2:3], dummy_seqs[3:], 
            dummy_labels, dummy_labels, sample_submission
        )
        
        # Run pipeline
        pipeline = ProductionPipeline(self.config)
        
        try:
            # This should work with fallback implementations
            train_loader, val_loader, test_loader = pipeline.load_and_prepare_data()
            pipeline.initialize_model()
            
            # Quick prediction test
            submission_df = pipeline.predict(test_loader)
            self.assertIsInstance(submission_df, pd.DataFrame)
            
            print("✓ Minimal pipeline run successful with fallback implementations")
            
        except Exception as e:
            self.fail(f"Minimal pipeline run failed: {str(e)}")

def run_tests():
    """Run all tests."""
    print("🧬 Running Production RNA Pipeline Tests")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestProductionPipeline))
    test_suite.addTest(unittest.makeSuite(TestProductionPipelineEnd2End))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("=" * 50)
    if result.wasSuccessful():
        print("🎉 All tests passed successfully!")
        print(f"✓ Ran {result.testsRun} tests")
    else:
        print(f"❌ {len(result.failures)} test(s) failed")
        print(f"❌ {len(result.errors)} error(s) occurred")
        for test, error in result.failures + result.errors:
            print(f"  - {test}: {error}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
